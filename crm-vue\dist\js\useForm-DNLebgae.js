var Ze=Object.defineProperty,et=Object.defineProperties;var tt=Object.getOwnPropertyDescriptors;var Me=Object.getOwnPropertySymbols;var nt=Object.prototype.hasOwnProperty,ot=Object.prototype.propertyIsEnumerable;var Ne=(e,o,l)=>o in e?Ze(e,o,{enumerable:!0,configurable:!0,writable:!0,value:l}):e[o]=l,P=(e,o)=>{for(var l in o||(o={}))nt.call(o,l)&&Ne(e,l,o[l]);if(Me)for(var l of Me(o))ot.call(o,l)&&Ne(e,l,o[l]);return e},Le=(e,o)=>et(e,tt(o));var T=(e,o,l)=>new Promise((i,C)=>{var A=d=>{try{r(l.next(d))}catch(f){C(f)}},s=d=>{try{r(l.throw(d))}catch(f){C(f)}},r=d=>d.done?i(d.value):Promise.resolve(d.value).then(A,s);r((l=l.apply(e,o)).next())});import{e as W,u as n,f as U,aq as ze,ar as X,aG as G,d as Se,I as st,q as Ye,B as at,k as N,i as rt,G as he,aE as ee,ag as Q,aB as ae,ah as fe,aJ as Be,aD as Y,at as lt,aA as it,au as Ae,w as ce,J as $e,h as ct,n as Ie,r as Ee,o as ut,v as dt,aO as ft,aK as _e,F as mt,aC as ve,aH as xe,b as pt}from"./vue-vendor-CnsIXr4W.js";import{a7 as ye,h as be,u as ht,W as bt,a5 as je,i as Pe,s as yt,aL as gt,a6 as At}from"./antd-vue-vendor-ac69AFxs.js";import{j as We,h as we}from"./componentMap-Bg13cs1o.js";import{a as vt,b as wt,c as Oe}from"./index-BNka1IZr.js";import{J as Ve,d as Z,h as ue,L as ke,_ as Re,$ as Ft,l as L,K as le,ah as Ct,U as Bt,ai as Pt,a4 as qe,j as Ot,k as Tt,F as I,aj as St,O as ge,N as ie,ak as It,al as jt,s as Ge,I as Te,am as Je,c as Vt,D as kt,an as Rt,ao as Dt,ap as Mt,G as He,H as Nt,aq as Lt}from"./index-BnxvmHFv.js";import"./index-j6u-UpGU.js";import{u as $t}from"./BasicModal-B-mPgnfO.js";const{t:Fe}=Ve();function Ke(e){return e.includes("Input")||e.includes("Complete")?Fe("common.inputText"):e.includes("Picker")||e.includes("Select")||e.includes("Cascader")||e.includes("Checkbox")||e.includes("Radio")||e.includes("Switch")?Fe("common.chooseText"):""}const Et=["DatePicker","MonthPicker","WeekPicker","TimePicker"];function _t(){return[...Et,"RangePicker"]}function xt(e,o,l){Reflect.has(e,"type")||(["DatePicker","MonthPicker","WeekPicker","TimePicker"].includes(o)?e.type=l?"string":"object":["RangePicker","Upload","CheckboxGroup","TimePicker"].includes(o)?e.type="array":["InputNumber"].includes(o)&&(e.type="number"))}function Wt(e,o){return e&&["Input","InputPassword","InputSearch","InputTextArea"].includes(e)&&o&&ke(o)?`${o}`:o}function qt(e,o){return e&&["InputNumber"].includes(e)&&typeof o=="string"&&o!=""?Number(o):o}const Ue=_t();function Ht(e,o){return W(()=>{const l=n(e),{labelCol:i={},wrapperCol:C={}}=l.itemProps||{},{labelWidth:A,disabledLabelWidth:s}=l,{labelWidth:r,labelCol:d,wrapperCol:f,layout:j}=n(o);if(s)return{labelCol:i,wrapperCol:C};if(!r&&!A&&!d)return i.style={textAlign:"left"},{labelCol:i,wrapperCol:C};let g=A||r,v=P(P({},d),i);const _=P(P({},f),C);return g&&(g=ke(g)?`${g}px`:g,v={}),{labelCol:P({style:{width:g||"100%"}},v),wrapperCol:P({style:{width:j==="vertical"?"100%":`calc(100% - ${g})`}},_)}})}const Kt=["id"],zt={__name:"Middleware",props:["formName","fieldName"],setup(e){const o=U(null),l=e;return l.formName&&l.fieldName&&(o.value=`${l.formName}_${l.fieldName}`),(i,C)=>(X(),ze("div",{id:o.value,style:{flex:"1",width:"100%"}},[G(i.$slots,"default",{},void 0,!0)],8,Kt))}},Yt=Re(zt,[["__scopeId","data-v-a1d963a0"]]);function me(e){return typeof e=="function"||Object.prototype.toString.call(e)==="[object Object]"&&!rt(e)}const Gt=Se({name:"BasicFormItem",inheritAttrs:!1,props:{schema:{type:Object,default:()=>({})},formProps:{type:Object,default:()=>({})},allDefaultValues:{type:Object,default:()=>({})},formModel:{type:Object,default:()=>({})},setFormModel:{type:Function,default:null},validateFields:{type:Function,default:null},tableAction:{type:Object},formActionType:{type:Object},clearValidate:{type:Function,default:null},formName:{type:String,default:""}},setup(e,{slots:o}){const{t:l}=Ve(),i=Ft(),{schema:C,formProps:A}=st(e),s=Ht(C,A),r=W(()=>{const{allDefaultValues:m,formModel:c,schema:p}=e,{mergeDynamicData:F}=e.formProps;return{field:p.field,model:c,values:P(P(P({},F),m),c),schema:p}}),d=W(()=>{var a;const{schema:m,tableAction:c,formModel:p,formActionType:F}=e;let{componentProps:t={}}=m;return L(t)&&(t=(a=t({schema:m,tableAction:c,formModel:p,formActionType:F}))!=null?a:{}),m.component==="Divider"&&(t=Object.assign({type:"horizontal",orientation:"left",plain:!0},t)),t}),f=W(()=>{const{disabled:m}=e.formProps;if(m)return m;const{dynamicDisabled:c}=e.schema,{disabled:p=!1}=n(d);let F=!!m||p;return le(c)&&(F=c),L(c)&&(F=c(n(r))),F}),j=W(()=>{const{dynamicPropsVal:m,dynamicPropskey:c}=e.schema;if(c==null)return null;{const{[c]:p}=n(d);let F=p;if(L(m))return F=m(n(r)),F}});function g(){const{show:m,ifShow:c}=e.schema,{showAdvancedButton:p}=e.formProps,F=p&&le(e.schema.isAdvanced)?e.schema.isAdvanced:!0;let t=!0,a=!0;return le(m)&&(t=m),le(c)&&(a=c),L(m)&&(t=m(n(r))),L(c)&&(a=c(n(r))),t=t&&F,{isShow:t,isIfShow:a}}let v=[],_=[];const S=m=>{v=[],_=[],m.forEach((c,p)=>{const F=c.validator;v.push(!0),_.push(null),L(F)&&(c.validator=(t,a,h)=>{if(v[p]){v[p]=!1,setTimeout(()=>{v[p]=!0},100);const u=F(t,a,h);return _[p]=u,u}else return _[p]})})};function D(){var M;const{rules:m=[],component:c,rulesMessageJoinLabel:p,label:F,dynamicRules:t,required:a,auth:h,field:u}=e.schema,{disabled:B}=e.formProps,{disabled:b=!1}=n(d);if(B||b)return e.clearValidate(u),[];const{hasPermission:R}=Bt(),{isShow:E}=g();if(h&&!R(h)||!E)return[];if(L(t)){const w=t(n(r));return a&&w.unshift({required:!0}),S(w),w}let O=be(m);const{rulesMessageJoinLabel:oe}=e.formProps,re=Reflect.has(e.schema,"rulesMessageJoinLabel")?p:oe,se=Ke(c)+`${re?F:""}`;function K(w,V){const k=w.message||se;return V===void 0||Pt(V)||Array.isArray(V)&&V.length===0||typeof V=="string"&&V.trim()===""||typeof V=="object"&&Reflect.has(V,"checked")&&Reflect.has(V,"halfChecked")&&Array.isArray(V.checked)&&Array.isArray(V.halfChecked)&&V.checked.length===0&&V.halfChecked.length===0?Promise.reject(k):Promise.resolve()}const te=L(a)?a(n(r)):a;(!O||O.length===0)&&te&&(O=[{required:te,validator:K}]);const ne=O.findIndex(w=>Reflect.has(w,"required")&&!Reflect.has(w,"validator"));if(ne!==-1){const w=O[ne],{isShow:V}=g();if(V||(w.required=!1),c){w.message=w.message||se,(c.includes("Input")||c.includes("Textarea"))&&(w.whitespace=!0);const k=(M=n(d))==null?void 0:M.valueFormat;xt(w,c,k)}}const y=O.findIndex(w=>w.max);return y!==-1&&!O[y].validator&&(O[y].message=O[y].message||l("component.form.maxTip",[O[y].max])),O.forEach(w=>{if(typeof w.pattern=="string")try{const V=new Function("item",`return ${w.pattern}`)(w);Object.prototype.toString.call(V)==="[object RegExp]"?w.pattern=V:w.pattern=new RegExp(w.pattern)}catch(V){w.pattern=new RegExp(w.pattern)}}),S(O),O}function $(){var V;const{renderComponentContent:m,component:c,field:p,changeEvent:F="change",valueField:t,componentProps:a,dynamicRules:h,rules:u=[]}=e.schema,B=c&&["Switch","Checkbox"].includes(c);let b=!1;c==="Input"&&a&&a.trim&&(b=!0);const R=`on${ht(F)}`,E=()=>[...L(h)?h(n(r)):[],...u],O={[R]:(...k)=>{const[H]=k;K[R]&&K[R](...k);const J=H?H.target:null;let z;J?B?z=J.checked:z=b?J.value.trim():J.value:z=H,e.setFormModel(p,z),E().find(de=>(de==null?void 0:de.trigger)==="blur")||e.validateFields([p]).catch(de=>{})}},oe=We.get(c),{autoSetPlaceHolder:re,size:se}=e.formProps,K=Le(P({allowClear:!0,getPopupContainer:k=>k==null?void 0:k.parentNode,size:se},n(d)),{disabled:n(f)}),te=e.schema.dynamicPropskey;if(te&&(K[te]=n(j)),!!re&&c!=="RangePicker"&&c){let k=L(e.schema.label)?e.schema.label():e.schema.label;i.getLocale==="en"&&!/^\s/.test(k)&&(k=" "+k),K.placeholder=((V=n(d))==null?void 0:V.placeholder)||Ke(c)+k}K.codeField=p,K.formValues=n(r);const y={[t||(B?"checked":"value")]:e.formModel[p]},M=P(P(P({},K),O),y);if(!m)return N(oe,M,null);const w=L(m)?P({},m(n(r))):{default:()=>m};return N(oe,M,me(w)?w:{default:()=>[w]})}function q(){const{label:m,helpMessage:c,helpComponentProps:p,subLabel:F,labelLength:t}=e.schema,a=L(m)?m():m;let h=a+"";t&&(h=h.substr(0,t));const B=F?N("span",null,[a,he(" "),N("span",{class:"text-secondary"},[F])]):t?N("label",{title:a},[h]):a,b=L(c)?c(n(r)):c;return!b||Array.isArray(b)&&b.length===0?B:N("span",null,[B,N(vt,ee({placement:"top",class:"mx-1",text:b},p),null)])}function x(){const{itemProps:m,slot:c,render:p,field:F,suffix:t,component:a}=e.schema,{labelCol:h,wrapperCol:u}=n(s),{colon:B}=e.formProps;if(a==="Divider"){let b;return N(ye,{span:24},{default:()=>[N(bt,n(d),me(b=q())?b:{default:()=>[b]})]})}else{let b;const R=()=>c?qe(o,c,n(r)):p?p(n(r)):$(),E=!!t,O=L(t)?t(n(r)):t;return N(je.Item,ee({name:F,colon:B,class:{"suffix-item":E}},m,{label:q(),rules:D(),validateFirst:!0,labelCol:h,wrapperCol:u}),{default:()=>[N("div",{style:"display:flex"},[N(Yt,{formName:e.formName,fieldName:F},me(b=R())?b:{default:()=>[b]}),E&&N("span",{class:"suffix"},[O])])]})}}return()=>{let m;const{colProps:c={},colSlot:p,renderColContent:F,component:t}=e.schema;if(!We.has(t))return null;const{baseColProps:a={}}=e.formProps,{getIsMobile:h}=Ct();let u;u=P(P({},a),c),c.span&&!n(h)&&["xs","sm","md","lg","xl","xxl"].forEach(O=>delete u[O]);const{isIfShow:B,isShow:b}=g(),R=n(r);return B&&Ye(N(ye,u,me(m=p?qe(o,p,R):F?F(R):x())?m:{default:()=>[m]}),[[at,b]])}}}),Xe=Symbol();function Jt(e){return Ot(e,Xe)}function Ut(){return Tt(Xe)}const Xt=Se({name:"BasicFormAction",components:{FormItem:je.Item,Button:St,BasicArrow:wt,[ye.name]:ye},props:{showActionButtonGroup:I.bool.def(!0),showResetButton:I.bool.def(!0),showSubmitButton:I.bool.def(!0),showAdvancedButton:I.bool.def(!0),resetButtonOptions:{type:Object,default:()=>({})},submitButtonOptions:{type:Object,default:()=>({})},actionColOptions:{type:Object,default:()=>({})},actionSpan:I.number.def(6),isAdvanced:I.bool,hideAdvanceBtn:I.bool,layout:I.oneOf(["horizontal","vertical","inline"]).def("horizontal")},emits:["toggle-advanced"],setup(e,{emit:o}){const{t:l}=Ve(),i=W(()=>{const{showAdvancedButton:r,actionSpan:d,actionColOptions:f}=e,j=24-d,g=r?{span:j<6?24:j}:{},v=e.layout=="inline"?{}:{span:r?6:4};return P(P(P({style:{textAlign:"right"}},v),g),f)}),C=W(()=>Object.assign({text:l("common.resetText"),preIcon:"ic:baseline-restart-alt"},e.resetButtonOptions)),A=W(()=>Object.assign({},{text:l("common.queryText"),preIcon:"ant-design:search-outlined"},e.submitButtonOptions));function s(){o("toggle-advanced")}return P({t:l,actionColOpt:i,getResetBtnOptions:C,getSubmitBtnOptions:A,toggleAdvanced:s},Ut())}});function Qt(e,o,l,i,C,A){const s=Q("Button"),r=Q("BasicArrow"),d=Q("FormItem"),f=Q("a-col");return e.showActionButtonGroup?(X(),ae(f,Be(ee({key:0},e.actionColOpt)),{default:Y(()=>[lt("div",{class:"btnArea",style:it([{width:"100%"},{textAlign:e.actionColOpt.style.textAlign}])},[N(d,null,{default:Y(()=>[G(e.$slots,"submitBefore",{},void 0,!0),e.showSubmitButton?(X(),ae(s,ee({key:0,type:"primary",class:"mr-2"},e.getSubmitBtnOptions,{onClick:e.submitAction}),{default:Y(()=>[he(Ae(e.getSubmitBtnOptions.text),1)]),_:1},16,["onClick"])):fe("",!0),G(e.$slots,"resetBefore",{},void 0,!0),e.showResetButton?(X(),ae(s,ee({key:1,type:"default",class:"mr-2"},e.getResetBtnOptions,{onClick:e.resetAction}),{default:Y(()=>[he(Ae(e.getResetBtnOptions.text),1)]),_:1},16,["onClick"])):fe("",!0),G(e.$slots,"advanceBefore",{},void 0,!0),e.showAdvancedButton&&!e.hideAdvanceBtn?(X(),ae(s,{key:2,type:"link",size:"small",onClick:e.toggleAdvanced},{default:Y(()=>[he(Ae(e.isAdvanced?e.t("component.form.putAway"):e.t("component.form.unfold"))+" ",1),N(r,{class:"ml-1",expand:!e.isAdvanced,up:""},null,8,["expand"])]),_:1},8,["onClick"])):fe("",!0),G(e.$slots,"advanceAfter",{},void 0,!0)]),_:3})],4)]),_:3},16)):fe("",!0)}const Zt=Re(Xt,[["render",Qt],["__scopeId","data-v-8803d71f"]]);function Qe(e,o){var C,A;return((C=n(e))==null?void 0:C.fieldMapToTime)&&(o=en(e,o)),((A=n(e))==null?void 0:A.fieldMapToNumber)&&(o=tn(e,o)),o}function en(e,o){const l=n(e).fieldMapToTime;if(!l||!Array.isArray(l))return o;for(const[i,[C,A],s="YYYY-MM-DD"]of l){if(!i||!C||!A||!o[i])continue;let r=o[i];Array.isArray(r)||(r=r.split(","));const[d,f]=r;d&&(o[C]=Z(d).format(s)),f&&(o[A]=Z(f).format(s)),Reflect.deleteProperty(o,i)}return o}function tn(e,o){const l=n(e).fieldMapToNumber;if(!l||!Array.isArray(l))return o;for(const[i,[C,A]]of l){if(!i||!C||!A||!o[i])continue;let s=o[i];typeof s=="string"&&(s=s.split(","));const[r,d]=s;o[C]=r,o[A]=d,Reflect.deleteProperty(o,i)}return o}function nn({defaultValueRef:e,getSchema:o,formModel:l,getProps:i}){function C(s){if(!ue(s))return{};const r={};for(const d of Object.entries(s)){let[,f]=d;const[j]=d;if(!j||ge(f)&&f.length===0||L(f))continue;const g=n(i).transformDateFunc;ue(f)&&(f=g==null?void 0:g(f)),ge(f)&&Pe.isDayjs(f[0])&&Pe.isDayjs(f[1])&&(f=f.map(v=>g==null?void 0:g(v))),ie(f)&&(f=f.trim()),yt(r,j,f)}return Qe(i,r)}function A(){const s=n(o),r={};s.forEach(d=>{const{defaultValue:f}=d;It(f)||(r[d.field]=f,l[d.field]=f)}),e.value=r}return{handleFormValues:C,initDefault:A}}const pe=24;function on({advanceState:e,emit:o,getProps:l,getSchema:i,formModel:C,defaultValueRef:A}){const{realWidthRef:s,screenEnum:r,screenRef:d}=jt(),f=W(()=>{if(!e.isAdvanced)return 0;const S=n(l).emptySpan||0;if(ke(S))return S;if(ue(S)){const{span:D=0}=S,$=n(d);return S[$.toLowerCase()]||D||0}return 0}),j=Ge(v,30);ce([()=>n(i),()=>e.isAdvanced,()=>n(s)],()=>{const{showAdvancedButton:S}=n(l);S&&j()},{immediate:!0});function g(S,D=0,$=!1,q=0){var a;const x=n(s),m=parseInt(S.md)||parseInt(S.xs)||parseInt(S.sm)||S.span||pe,c=parseInt(S.lg)||m,p=parseInt(S.xl)||c,F=parseInt(S.xxl)||p;x<=r.LG?D+=m:x<r.XL?D+=c:x<r.XXL?D+=p:D+=F;let t=(a=n(l).autoAdvancedCol)!=null?a:3;return $?(e.hideAdvanceBtn=n(i).length<=t,e.isLoad||(e.isLoad=!0,e.isAdvanced=!e.isAdvanced,n(i).length>t&&(e.hideAdvanceBtn=!1,e.isAdvanced=!1)),{isAdvanced:e.isAdvanced,itemColSum:D}):D>pe*(n(l).alwaysShowLines||1)?{isAdvanced:e.isAdvanced,itemColSum:D}:!e.isAdvanced&&q+1>t?{isAdvanced:!1,itemColSum:D}:{isAdvanced:!0,itemColSum:D}}function v(){let S=0,D=0;const{baseColProps:$={}}=n(l),q=n(i);for(let x=0;x<q.length;x++){const m=q[x],{show:c,colProps:p}=m;let F=!0;if(le(c)&&(F=c),L(c)&&(F=c({schema:m,model:C,field:m.field,values:P(P({},n(A)),C)})),F&&(p||$)){const{itemColSum:t,isAdvanced:a}=g(P(P({},$),p),S,!1,x);S=t||0,a&&(D=S),m.isAdvanced=a}}e.actionSpan=D%pe+n(f),g(n(l).actionColOptions||{span:pe},S,!0),o("advanced-change")}function _(){e.isAdvanced=!e.isAdvanced}return{handleToggleAdvanced:_}}function sn({emit:e,getProps:o,formModel:l,getSchema:i,defaultValueRef:C,formElRef:A,schemaRef:s,handleFormValues:r}){function d(){return T(this,null,function*(){const{resetFunc:t,submitOnReset:a}=n(o);t&&L(t)&&(yield t()),n(A)&&(Object.keys(l).forEach(u=>{l[u]=C.value[u]}),c(),e("reset",$e(l)),a&&F())})}function f(t){return T(this,null,function*(){const a=n(i).map(u=>u.field).filter(Boolean),h=[];Object.keys(t).forEach(u=>{const B=n(i).find(E=>E.field===u);let b=t[u];if(!(t instanceof Object))return;const R=Reflect.has(t,u);if(b=Wt(B==null?void 0:B.component,b),b=qt(B==null?void 0:B.component,b),R&&a.includes(u)){if(q(u))if(Array.isArray(b)){const E=[];for(const O of b)E.push(O?Z(O):null);l[u]=E}else{const{componentProps:E}=B||{};let O=E;typeof E=="function"&&(O=O({formModel:l})),l[u]=b?O!=null&&O.valueFormat?b:Z(b):null}else l[u]=b;h.push(u)}}),x(h).catch(u=>{})})}function j(t){if(!ie(t))return null;const a=n(i),h=a.findIndex(u=>u.field===t);return h!==-1?be(a[h]):null}function g(t){return T(this,null,function*(){const a=be(n(i));if(!t)return;let h=ie(t)?[t]:t;ie(t)&&(h=[t]);for(const u of h)v(u,a);s.value=a})}function v(t,a){if(ie(t)){const h=a.findIndex(u=>u.field===t);h!==-1&&(delete l[t],a.splice(h,1))}}function _(t,a,h=!1){return T(this,null,function*(){const u=be(n(i)),B=u.findIndex(R=>R.field===a);if(u.some(R=>R.field===a||t.field)){if(!a||B===-1||h){h?u.unshift(t):u.push(t),s.value=u;return}B!==-1&&u.splice(B+1,0,t),s.value=u}})}function S(t){return T(this,null,function*(){let a=[];if(ue(t)&&a.push(t),ge(t)&&(a=[...t]),!a.every(u=>u.component==="Divider"||Reflect.has(u,"field")&&u.field)){Te("All children of the form Schema array that need to be updated must contain the `field` field");return}s.value=a})}function D(t){return T(this,null,function*(){let a=[];if(ue(t)&&a.push(t),ge(t)&&(a=[...t]),!a.every(B=>B.component==="Divider"||Reflect.has(B,"field")&&B.field)){Te("All children of the form Schema array that need to be updated must contain the `field` field");return}const u=[];a.forEach(B=>{n(i).forEach(b=>{if(b.field===B.field){const R=Je(b,B);u.push(R)}else u.push(b)})}),s.value=gt(u,"field")})}function $(){return n(A)?r($e(n(l))):{}}function q(t){return n(i).some(a=>a.field===t?Ue.includes(a.component):!1)}function x(t,a){return T(this,null,function*(){var h;return(h=n(A))==null?void 0:h.validateFields(t,a)})}function m(t){return T(this,null,function*(){var a;return yield(a=n(A))==null?void 0:a.validate(t)})}function c(t){return T(this,null,function*(){var a;yield(a=n(A))==null?void 0:a.clearValidate(t)})}function p(t,a){return T(this,null,function*(){var h;yield(h=n(A))==null?void 0:h.scrollToField(t,a)})}function F(t){return T(this,null,function*(){t&&t.preventDefault();const{submitFunc:a}=n(o);if(a&&L(a)){yield a();return}if(n(A))try{const u=yield m();for(let b in u)u[b]instanceof Array&&Vt(o,b)==="string"&&(u[b]=u[b].join(","));const B=r(u);e("submit",B)}catch(u){e("submit",{})}})}return{handleSubmit:F,clearValidate:c,validate:m,validateFields:x,getFieldsValue:$,updateSchema:D,resetSchema:S,getSchemaByField:j,appendSchemaByField:_,removeSchemaByFiled:g,resetFields:d,setFieldsValue:f,scrollToField:p}}function an(C){return T(this,arguments,function*({getSchema:e,getProps:o,formElRef:l,isInitedDefault:i}){ct(()=>T(this,null,function*(){if(n(i)||!n(o).autoFocusFirstItem)return;yield Ie();const A=n(e),s=n(l),r=s==null?void 0:s.$el;if(!s||!r||!A||A.length===0||!A[0].component.includes("Input"))return;const f=r.querySelector(".ant-row:first-child input");f&&(f==null||f.focus())}))})}const{form:Ce}=Oe,rn={model:{type:Object,default:{}},labelWidth:{type:[Number,String],default:0},fieldMapToTime:{type:Array,default:()=>[]},fieldMapToNumber:{type:Array,default:()=>[]},compact:I.bool,schemas:{type:[Array],default:()=>[]},mergeDynamicData:{type:Object,default:null},baseRowStyle:{type:Object},baseColProps:{type:Object},autoSetPlaceHolder:I.bool.def(!0),autoSubmitOnEnter:I.bool.def(!1),submitOnReset:I.bool,size:I.oneOf(["default","small","large"]).def("default"),disabled:I.bool,emptySpan:{type:[Number,Object],default:0},showAdvancedButton:I.bool,transformDateFunc:{type:Function,default:e=>Pe.isDayjs(e)?e==null?void 0:e.format("YYYY-MM-DD HH:mm:ss"):e},rulesMessageJoinLabel:I.bool.def(!0),autoAdvancedCol:I.number.def(3),alwaysShowLines:I.number.def(1),showActionButtonGroup:I.bool.def(!0),actionColOptions:Object,showResetButton:I.bool.def(!0),autoFocusFirstItem:I.bool,resetButtonOptions:Object,showSubmitButton:I.bool.def(!0),submitButtonOptions:Object,resetFunc:Function,submitFunc:Function,hideRequiredMark:I.bool,labelCol:{type:Object,default:Ce.labelCol},layout:I.oneOf(["horizontal","vertical","inline"]).def("horizontal"),tableAction:{type:Object},wrapperCol:{type:Object,default:Ce.wrapperCol},colon:I.bool.def(Ce.colon),labelAlign:I.string,rowProps:Object,autoSearch:I.bool.def(!1)},ln=Se({name:"BasicForm",components:{FormItem:Gt,Form:je,Row:At,FormAction:Zt},props:rn,emits:["advanced-change","reset","submit","register"],setup(e,{emit:o,attrs:l}){const i=Ee({}),C=$t(),A=Ee({isAdvanced:!1,hideAdvanceBtn:!0,isLoad:!1,actionSpan:6}),s=U({}),r=U(!1),d=U({}),f=U(null),j=U(null),{prefixCls:g}=kt("basic-form"),v=W(()=>{let y=P(P({},e),n(d));return y.labelWidth&&(y.labelCol=void 0),y.layout==="inline"&&(y.labelCol===Oe.form.labelCol&&(y.labelCol=void 0),y.wrapperCol===Oe.form.wrapperCol&&(y.wrapperCol=void 0)),y}),_=W(()=>[g,{[`${g}--compact`]:n(v).compact,"jeecg-form-detail-effect":n(v).disabled}]),S=W(()=>{const{baseRowStyle:y={},rowProps:M}=n(v);return P({style:y},M)}),D=W(()=>P(P(P({},l),e),n(v))),$=W(()=>{const y=n(f)||n(v).schemas;for(const M of y){const{defaultValue:w,component:V,componentProps:k}=M;if(w&&Ue.includes(V)){let H="";if(k&&(H=k==null?void 0:k.valueFormat),!Array.isArray(w))H?M.defaultValue=Z(w,H).format(H):M.defaultValue=Z(w);else{const J=[];w.forEach(z=>{H?J.push(Z(z,H).format(H)):J.push(Z(z))}),J.forEach((z,De)=>{w[De]=z})}}}return n(v).showAdvancedButton?y.filter(M=>M.component!=="Divider"):y}),{handleToggleAdvanced:q}=on({advanceState:A,emit:o,getProps:v,getSchema:$,formModel:i,defaultValueRef:s}),{handleFormValues:x,initDefault:m}=nn({getProps:v,defaultValueRef:s,getSchema:$,formModel:i});an({getSchema:$,getProps:v,isInitedDefault:r,formElRef:j});const{handleSubmit:c,setFieldsValue:p,clearValidate:F,validate:t,validateFields:a,getFieldsValue:h,updateSchema:u,resetSchema:B,getSchemaByField:b,appendSchemaByField:R,removeSchemaByFiled:E,resetFields:O,scrollToField:oe}=sn({emit:o,getProps:v,formModel:i,getSchema:$,defaultValueRef:s,formElRef:j,schemaRef:f,handleFormValues:x});Jt({resetAction:O,submitAction:c}),ce(()=>n(v).model,()=>{const{model:y}=n(v);y&&p(y)},{immediate:!0}),ce(()=>n(v).schemas,y=>{B(y!=null?y:[])}),ce(()=>$.value,y=>{Ie(()=>{var M;(M=C==null?void 0:C.redoModalHeight)==null||M.call(C)}),!n(r)&&y!=null&&y.length&&(m(),r.value=!0)});function re(y){return T(this,null,function*(){d.value=Je(n(d)||{},y)})}const se=Ge(c,300);function K(y,M){i[y]=M,e.autoSearch===!0&&se()}function te(y){const{autoSubmitOnEnter:M}=n(v);if(M&&y.key==="Enter"&&y.target&&y.target instanceof HTMLElement){const w=y.target;w&&w.tagName&&w.tagName.toUpperCase()=="INPUT"&&c()}}const ne={getFieldsValue:h,setFieldsValue:p,resetFields:O,updateSchema:u,resetSchema:B,setProps:re,getProps:v,getSchemaByField:b,removeSchemaByFiled:E,appendSchemaByField:R,clearValidate:F,validateFields:a,validate:t,submit:c,scrollToField:oe};return ut(()=>{m(),o("register",ne)}),P({getBindValue:D,handleToggleAdvanced:q,handleEnterPress:te,formModel:i,defaultValueRef:s,advanceState:A,getRow:S,getProps:v,formElRef:j,getSchema:$,formActionType:ne,setFormModel:K,getFormClass:_,getFormActionBindProps:W(()=>P(P({},v.value),A))},ne)}});function cn(e,o,l,i,C,A){const s=Q("FormItem"),r=Q("FormAction"),d=Q("Row"),f=Q("Form"),j=dt("auth");return X(),ae(f,ee(e.getBindValue,{class:e.getFormClass,ref:"formElRef",model:e.formModel,onKeypress:ft(e.handleEnterPress,["enter"])}),{default:Y(()=>[N(d,Be(_e(e.getRow)),{default:Y(()=>[G(e.$slots,"formHeader"),(X(!0),ze(mt,null,ve(e.getSchema,g=>Ye((X(),ae(s,{key:g.field,tableAction:e.tableAction,formActionType:e.formActionType,schema:g,formProps:e.getProps,allDefaultValues:e.defaultValueRef,formModel:e.formModel,formName:e.getBindValue.name,setFormModel:e.setFormModel,validateFields:e.validateFields,clearValidate:e.clearValidate},xe({_:2},[ve(Object.keys(e.$slots),v=>({name:v,fn:Y(_=>[G(e.$slots,v,ee({ref_for:!0},_||{}))])}))]),1032,["tableAction","formActionType","schema","formProps","allDefaultValues","formModel","formName","setFormModel","validateFields","clearValidate"])),[[j,g.auth]])),128)),N(r,ee(e.getFormActionBindProps,{onToggleAdvanced:e.handleToggleAdvanced}),xe({_:2},[ve(["resetBefore","submitBefore","advanceBefore","advanceAfter"],g=>({name:g,fn:Y(v=>[G(e.$slots,g,Be(_e(v||{})))])}))]),1040,["onToggleAdvanced"]),G(e.$slots,"formFooter")]),_:3},16)]),_:3},16,["class","model","onKeypress"])}const gn=Re(ln,[["render",cn]]);function An(e){const o=U(null),l=U(!1);we("OnlineSelectCascade",Rt),we("LinkTableCard",Dt),we("LinkTableSelect",Mt);function i(){return T(this,null,function*(){const s=n(o);return s||Te("The form instance has not been obtained, please make sure that the form has been rendered when performing the form operation!"),yield Ie(),s})}function C(s){He()&&pt(()=>{o.value=null,l.value=null}),!(n(l)&&He()&&s===n(o))&&(o.value=s,l.value=!0,ce(()=>e,()=>{e&&s.setProps(Nt(e))},{immediate:!0,deep:!0}))}return[C,{scrollToField:(s,r)=>T(this,null,function*(){(yield i()).scrollToField(s,r)}),setProps:s=>T(this,null,function*(){(yield i()).setProps(s)}),updateSchema:s=>T(this,null,function*(){(yield i()).updateSchema(s)}),resetSchema:s=>T(this,null,function*(){(yield i()).resetSchema(s)}),clearValidate:s=>T(this,null,function*(){(yield i()).clearValidate(s)}),resetFields:()=>T(this,null,function*(){i().then(s=>T(this,null,function*(){yield s.resetFields()}))}),removeSchemaByFiled:s=>T(this,null,function*(){var r;(r=n(o))==null||r.removeSchemaByFiled(s)}),getFieldsValue:()=>{var r;let s=(r=n(o))==null?void 0:r.getFieldsValue();return s&&Object.keys(s).map(d=>{s[d]instanceof Array&&(typeof(s[d][0]||"")=="object"||(s[d]=s[d].join(",")))}),s},setFieldsValue:s=>T(this,null,function*(){(yield i()).setFieldsValue(s)}),appendSchemaByField:(s,r,d)=>T(this,null,function*(){(yield i()).appendSchemaByField(s,r,d)}),submit:()=>T(this,null,function*(){return(yield i()).submit()}),validate:s=>T(this,null,function*(){const r=yield i();let d=e||r.getProps;return r.validate(s).then(j=>{for(let g in j)j[g]instanceof Array&&Lt(r.getSchemaByField(g))==="string"&&(j[g]=j[g].join(","));return Qe(d,j)})}),validateFields:(s,r)=>T(this,null,function*(){return(yield i()).validateFields(s,r)})}]}export{gn as B,An as u};
