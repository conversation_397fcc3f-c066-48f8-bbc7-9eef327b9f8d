<template>
  <div>
    <!--引用表格-->
    <BasicTable @register="registerTable" :rowSelection="rowSelection">
      <!--插槽:table标题-->
      <template #tableTitle>
        <!-- <a-button type="primary" @click="handleAdd" preIcon="ant-design:plus-outlined"> 新增</a-button> -->
        <a-button type="primary" preIcon="ant-design:export-outlined" @click="onExportXls"> 导出</a-button>
        <!-- <a-button type="primary" preIcon="ant-design:import-outlined" @click="onImportXls"> 导入</a-button> -->
        <a-dropdown v-if="selectedRowKeys.length > 0">
          <template #overlay>
            <a-menu>
              <a-menu-item key="1" @click="batchHandleDelete">
                <Icon icon="ant-design:delete-outlined"></Icon>
                删除
              </a-menu-item>
            </a-menu>
          </template>
          <a-button>批量操作
            <Icon icon="mdi:chevron-down"></Icon>
          </a-button>
        </a-dropdown>
      </template>
      <!--操作栏-->
      <!-- <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" />
      </template> -->
      <!--字段回显插槽-->
      <!-- <template #htmlSlot="{text}">
        <div v-html="text"></div>
      </template> -->

    </BasicTable>
    <!-- 表单区域 -->
    <CrmCommissionCalculationModal @register="registerModal" @success="handleSuccess"></CrmCommissionCalculationModal>
  </div>
</template>

<script lang="ts" name="crmcommissioncalculation-crmCommissionCalculation" setup>
  import {ref, computed, unref} from 'vue';
  import {BasicTable, useTable, TableAction} from '/@/components/Table';
  import {useModal} from '/@/components/Modal';
  import {useListPage} from '/@/hooks/system/useListPage';
  import CrmCommissionCalculationModal from './modules/CrmCommissionCalculationModal.vue'
  import {columns, searchFormSchema} from './CrmCommissionCalculation.data';
  import {queryCommissionInfo, deleteOne, batchDelete, getExportCommissionInfoUrl, getImportUrl} from './CrmCommissionCalculation.api';
  import {downloadByOnlineUrl} from '/@/utils/file/download';
  import {useUserStore} from '/@/store/modules/user';
  import {useMessage} from '/@/hooks/web/useMessage';

  const {createMessage} = useMessage();

  const checkedKeys = ref<Array<string | number>>([]);
  const userStore = useUserStore();
  //注册model
  const [registerModal, {openModal}] = useModal();
  
  //注册table数据
  const { tableContext, onExportXls } = useListPage({
    tableProps: {
      title: '保险公司账单列表',
      api: queryCommissionInfo,
      columns,
      canResize: false,
      formConfig: {
        labelWidth: 120,
        schemas: searchFormSchema,
        autoSubmitOnEnter: true,
        showAdvancedButton: true,
        fieldMapToTime: [
          ['calculationDateRange', ['calculationDateStart', 'calculationDateEnd'], 'YYYY-MM-DD']
        ],
      },
      actionColumn: {
        width: 180,
      },
      beforeFetch: (params) => {
        console.log('查询参数:', params);
        return params;
      },
    },
    exportConfig: {
      name: "保险公司账单列表",
      url: getExportCommissionInfoUrl,
    },
  });

  const [registerTable, { reload }, { rowSelection, selectedRowKeys }] = tableContext;

  // const {prefixCls, tableContext, onExportXls, onImportXls} = useListPage({
  //   tableApi: queryCommissionInfo,
  //   exportApi: getExportCommissionInfoUrl,
  //   importApi: getImportUrl,
  // })

  // const [registerTable, {reload}, {rowSelection, selectedRowKeys}] = useTable({
  //   title: '保险公司账单列表',
  //   api: queryCommissionInfo,
  //   rowKey: 'id',
  //   columns,
  //   formConfig: {
  //     //labelWidth: 120,
  //     schemas: searchFormSchema,
  //     autoSubmitOnEnter: true,
  //     showAdvancedButton: true,
  //     fieldMapToNumber: [],
  //     fieldMapToTime: [
  //       ['calculationDateStart', 'calculationDateEnd', 'YYYY-MM-DD']
  //     ],
  //   },
  //   useSearchForm: true,
  //   showTableSetting: true,
  //   bordered: true,
  //   handleSearchInfoFn(info) {
  //     console.log('handleSearchInfoFn', info);
  //     return info;
  //   },
  //   actionColumn: {
  //     width: 120,
  //     fixed: 'right'
  //   },
  //   rowSelection: {
  //     type: 'checkbox',
  //   },
  // });

  /**
   * 新增事件
   */
  function handleAdd() {
    openModal(true, {
      isUpdate: false,
    });
  }
  /**
   * 编辑事件
   */
  function handleEdit(record: Recordable) {
    openModal(true, {
      record,
      isUpdate: true,
    });
  }
  /**
   * 详情
   */
  function handleDetail(record: Recordable) {
    openModal(true, {
      record,
      isUpdate: false,
    });
  }
  /**
   * 删除事件
   */
  async function handleDelete(record) {
    await deleteOne({id: record.id}, handleSuccess);
  }
  /**
   * 批量删除事件
   */
  async function batchHandleDelete() {
    await batchDelete({ids: selectedRowKeys.value}, handleSuccess);
  }
  /**
   * 成功回调
   */
  function handleSuccess() {
    (selectedRowKeys.value = []) && reload();
  }
  /**
   * 操作栏
   */
  function getTableAction(record){
    return [
      {
        label: '编辑',
        onClick: handleEdit.bind(null, record),
      }
    ]
  }
  /**
   * 下载文件
   */
  function downloadFile(text){
    if(!text){
      createMessage.warning("未知的文件");
      return;
    }
    if(text.indexOf(",")>0){
      text = text.substring(0,text.indexOf(","))
    }
    downloadByOnlineUrl(text);
  }

  function getAreaTextByCode(code) {
    // TODO: 实现省市区代码转换
    return code;
  }
</script>

<style scoped>

</style>
