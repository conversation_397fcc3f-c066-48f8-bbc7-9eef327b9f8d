import{d as L,f as O,w as C,aI as G,aU as W,aV as z,at as k,ag as n,aB as r,ar as i,as as H,aD as S,aq as s,F as m,k as y,aC as $,G as Q,au as j,aE as E}from"./vue-vendor-CnsIXr4W.js";import"./index-D4YmpXsT.js";import R from"./JOnlineSearchSelect-22aae934-DAkADZoz.js";import{cl as X}from"./index-BnxvmHFv.js";import{s as Z,r as ee,n as le,e as ae,h as te,l as ie}from"./JAddInput-B7aUDeJi.js";import re from"./JAreaLinkage-BDz8pfno.js";import{o as oe}from"./JPopup-gJ1mXmQZ.js";import{i as ne}from"./JSelectDept-1TdcISOX.js";import{u as ue}from"./JSelectUser-Blw_A8N4.js";import"./useForm-DNLebgae.js";import"./antd-vue-vendor-ac69AFxs.js";import"./componentMap-Bg13cs1o.js";import"./useFormItem-Eoh0MXq5.js";import"./index-j6u-UpGU.js";import"./BasicModal-B-mPgnfO.js";import"./useTimeout-DoPujGLn.js";import"./vxe-table-vendor-CK0mysZD.js";import"./useWindowSizeFn-BcaCTZPc.js";import"./index-BNka1IZr.js";import"./index-DmtYkh1u.js";import"./index-B4xK7O2u.js";import"./useIntersectionObserver-DZYoTRf_.js";import"./download-ZmOT1Dx2.js";import"./base64Conver-24EVOS6V.js";import"./index-DBQnQ9LG.js";import"./index-CUrp7h3O.js";import"./useCountdown-D_p0DKZc.js";import"./useFormItemSingle-D8EqzImO.js";import"./JEllipsis-IqMnhApY.js";import"./JUpload-DCci6z5o.js";import"./index-C3YRp4LI.js";import"./index-mK2t00gc.js";import"./JCodeEditor-C8R0_wo6.js";import"./htmlmixed-XbbCJteX.js";import"./vue-BLlfMlNv.js";/* empty css             */import"./EasyCronInput-BB8IcbzA.js";import"./props-DHLM7ils.js";import"./JSelectBiz-rbVGd4Dv.js";import"./areaDataUtil-ChdaTOUz.js";import"./index-86WEzeSw.js";import"./index-Dr3sBsWR.js";import"./bem-DfjdT9o5.js";import"./props-WP5sspMu.js";import"./useContextMenu-DwGGVBbH.js";import"./depart.api-BTFSMQ51.js";var de=Object.defineProperty,I=Object.getOwnPropertySymbols,pe=Object.prototype.hasOwnProperty,ce=Object.prototype.propertyIsEnumerable,K=(e,l,o)=>l in e?de(e,l,{enumerable:!0,configurable:!0,writable:!0,value:o}):e[l]=o,se=(e,l)=>{for(var o in l||(l={}))pe.call(l,o)&&K(e,o,l[o]);if(I)for(var o of I(l))ce.call(l,o)&&K(e,o,l[o]);return e};const M=L({name:"OnlineSearchFormItem",components:{JOnlineSearchSelect:R,JDictSelectTag:ie,JTreeSelect:te,JCategorySelect:ae,JSelectUser:ue,JSelectUserByDept:le,JSelectDept:ne,JPopup:oe,JAreaLinkage:re,JAreaSelect:ee,JSelectMultiple:Z},props:{value:{type:String,default:""},item:{type:Object,default:()=>{},required:!0},dictOptions:{type:Object,default:()=>{},required:!1},onlineForm:{type:Object,default:()=>{},required:!1}},emits:["update:value","change"],setup(e,{emit:l}){const o="120px",q={style:{"max-width":o}},B="single";let f=O(""),u=O(""),g=O("");C(()=>e.value,()=>{b()?f.value=e.value?e.value:void 0:f.value=e.value,e.value||(u.value="",g.value="")},{deep:!0,immediate:!0}),C(f,t=>{l("update:value",t)},{immediate:!0}),C(u,t=>{l("change",e.item.field+"_begin",t),l("update:value","1")}),C(g,t=>{l("change",e.item.field+"_end",t),l("update:value","1")});function D(t){return t.dbField?t.dbField:t.field}function b(){let t=e.item;return t?t.view=="list"||t.view=="radio"||t.view=="switch":!1}function T(){let t=e.item;return t.dictTable&&t.dictTable.length>0?t.dictTable+","+t.dictText+","+t.dictCode:t.dictCode}function x(){let t=e.item,{dictTable:a,dictCode:d,dictText:v}=t,p=a.toLowerCase().split("where"),h="";return p.length>1&&(h=" where"+p[1]),"select "+d+" as 'value', "+v+" as 'text' from "+p[0]+h}function F(t){let{dictText:a,dictCode:d}=t;if(!a||a.length==0)return[];let v=a.split(","),p=d.split(","),h=[];for(let c=0;c<v.length;c++)h.push({target:v[c],source:p[c]});return h}function U(t){let{dictText:a}=e.item,d=a.split(",")[0];l("change",d,t[d])}function Y(t){l("update:value",t)}function w(t,a,d){let v={labelKey:a,rowKey:d},p=t.fieldExtendJson;if(p&&typeof p=="string"){let h=JSON.parse(p),c=se({},h);c.text&&(v.labelKey=c.text),c.store&&(v.rowKey=c.store)}return v}let J=w(e.item,"realname","username"),_=w(e.item,"departName","id");function V(t){t&&t.length>0?l("update:value",t.join(",")):l("update:value","")}return{getPopupFieldConfig:F,userSelectProp:J,depSelectProp:_,handleSelectChange:V,setFieldsValue:U,innerValue:f,beginValue:u,endValue:g,isEasySelect:b,getDictOptionKey:D,getDictCode:T,labelTextMaxWidth:o,labelCol:q,single_mode:B,getSqlByDictCode:x,handleCategoryTreeChange:Y}}}),N=()=>{G(e=>({"6524b8a9":e.labelTextMaxWidth}))},A=M.setup;M.setup=A?(e,l)=>(N(),A(e,l)):N;const me=M,P=e=>(W("data-v-e62a9629"),e=e(),z(),e),ve=["title"],he=P(()=>k("span",{class:"group-query-strig"},"~",-1)),ge=P(()=>k("span",{class:"group-query-strig"},"~",-1)),ye=P(()=>k("span",{class:"group-query-strig"},"~",-1));function fe(e,l,o,q,B,f){const u=n("a-date-picker"),g=n("JDictSelectTag"),D=n("a-select-option"),b=n("a-select"),T=n("JTreeSelect"),x=n("JCategorySelect"),F=n("JOnlineSearchSelect"),U=n("JSelectUser"),Y=n("JSelectDept"),w=n("JPopup"),J=n("JAreaSelect"),_=n("JSelectMultiple"),V=n("a-input"),t=n("a-form-item");return i(),r(t,{labelCol:e.labelCol,class:H("jeecg-online-search")},{label:S(()=>[k("span",{title:e.item.label,class:"label-text"},j(e.item.label),9,ve)]),default:S(()=>[e.item.view=="date"?(i(),s(m,{key:0},[e.single_mode===e.item.mode?(i(),r(u,{key:0,style:{width:"100%"},showTime:!1,valueFormat:"YYYY-MM-DD",placeholder:"请选择"+e.item.label,value:e.innerValue,"onUpdate:value":l[0]||(l[0]=a=>e.innerValue=a)},null,8,["placeholder","value"])):(i(),s(m,{key:1},[y(u,{showTime:!1,valueFormat:"YYYY-MM-DD",placeholder:"开始日期",value:e.beginValue,"onUpdate:value":l[1]||(l[1]=a=>e.beginValue=a),style:{width:"calc(50% - 15px)"}},null,8,["value"]),he,y(u,{showTime:!1,valueFormat:"YYYY-MM-DD",placeholder:"结束日期",value:e.endValue,"onUpdate:value":l[2]||(l[2]=a=>e.endValue=a),style:{width:"calc(50% - 15px)"}},null,8,["value"])],64))],64)):e.item.view=="datetime"?(i(),s(m,{key:1},[e.single_mode===e.item.mode?(i(),r(u,{key:0,style:{width:"100%"},showTime:!0,valueFormat:"YYYY-MM-DD hh:mm:ss",placeholder:"请选择"+e.item.label,value:e.innerValue,"onUpdate:value":l[3]||(l[3]=a=>e.innerValue=a)},null,8,["placeholder","value"])):(i(),s(m,{key:1},[y(u,{showTime:!0,valueFormat:"YYYY-MM-DD hh:mm:ss",placeholder:"开始时间",value:e.beginValue,"onUpdate:value":l[4]||(l[4]=a=>e.beginValue=a),style:{width:"calc(50% - 15px)"}},null,8,["value"]),ge,y(u,{showTime:!0,valueFormat:"YYYY-MM-DD hh:mm:ss",placeholder:"结束时间",value:e.endValue,"onUpdate:value":l[5]||(l[5]=a=>e.endValue=a),style:{width:"calc(50% - 15px)"}},null,8,["value"])],64))],64)):e.isEasySelect()?(i(),s(m,{key:2},[e.item.config==="1"?(i(),r(g,{key:0,placeholder:"请选择"+e.item.label,value:e.innerValue,"onUpdate:value":l[6]||(l[6]=a=>e.innerValue=a),dictCode:e.getDictCode()},null,8,["placeholder","value","dictCode"])):(i(),r(b,{key:1,placeholder:"请选择"+e.item.label,value:e.innerValue,"onUpdate:value":l[7]||(l[7]=a=>e.innerValue=a)},{default:S(()=>[(i(!0),s(m,null,$(e.dictOptions[e.getDictOptionKey(e.item)],(a,d)=>(i(),r(D,{key:d,value:a.value},{default:S(()=>[Q(j(a.text),1)]),_:2},1032,["value"]))),128))]),_:1},8,["placeholder","value"]))],64)):e.item.view==="sel_tree"?(i(),r(T,{key:3,placeholder:"请选择"+e.item.label,value:e.innerValue,"onUpdate:value":l[8]||(l[8]=a=>e.innerValue=a),dict:e.item.dict,pidField:e.item.pidField,pidValue:e.item.pidValue,hasChildField:e.item.hasChildField,"load-triggle-change":""},null,8,["placeholder","value","dict","pidField","pidValue","hasChildField"])):e.item.view==="cat_tree"?(i(),r(x,{key:4,onChange:e.handleCategoryTreeChange,loadTriggleChange:!0,pcode:e.item.pcode,value:e.innerValue,"onUpdate:value":l[9]||(l[9]=a=>e.innerValue=a),placeholder:"请选择"+e.item.label},null,8,["onChange","pcode","value","placeholder"])):e.item.view==="sel_search"?(i(),s(m,{key:5},[e.item.config==="1"?(i(),r(g,{key:0,value:e.innerValue,"onUpdate:value":l[10]||(l[10]=a=>e.innerValue=a),placeholder:"请选择"+e.item.label,dict:e.getDictCode()},null,8,["value","placeholder","dict"])):(i(),r(F,{key:1,value:e.innerValue,"onUpdate:value":l[11]||(l[11]=a=>e.innerValue=a),placeholder:"请选择"+e.item.label,sql:e.getSqlByDictCode()},null,8,["value","placeholder","sql"]))],64)):e.item.view=="sel_user"?(i(),r(U,E({key:6},e.userSelectProp,{value:e.innerValue,"onUpdate:value":l[12]||(l[12]=a=>e.innerValue=a),placeholder:"请选择"+e.item.label}),null,16,["value","placeholder"])):e.item.view=="sel_depart"?(i(),r(Y,E({key:7,showButton:!1},e.depSelectProp,{value:e.innerValue,"onUpdate:value":l[13]||(l[13]=a=>e.innerValue=a),placeholder:"请选择"+e.item.label}),null,16,["value","placeholder"])):e.item.view=="popup"?(i(),r(w,{key:8,placeholder:"请选择"+e.item.label,value:e.innerValue,"onUpdate:value":l[14]||(l[14]=a=>e.innerValue=a),code:e.item.dictTable,setFieldsValue:e.setFieldsValue,"field-config":e.getPopupFieldConfig(e.item),multi:!0},null,8,["placeholder","value","code","setFieldsValue","field-config"])):e.item.view=="pca"?(i(),r(J,{key:9,placeholder:"请选择"+e.item.label,value:e.innerValue,"onUpdate:value":l[15]||(l[15]=a=>e.innerValue=a)},null,8,["placeholder","value"])):e.item.view=="checkbox"||e.item.view=="list_multi"?(i(),r(_,{key:10,dictCode:e.getDictCode(),placeholder:"请选择"+e.item.label,value:e.innerValue,"onUpdate:value":l[16]||(l[16]=a=>e.innerValue=a)},null,8,["dictCode","placeholder","value"])):(i(),s(m,{key:11},[e.single_mode===e.item.mode?(i(),r(V,{key:0,placeholder:"请选择"+e.item.label,value:e.innerValue,"onUpdate:value":l[17]||(l[17]=a=>e.innerValue=a)},null,8,["placeholder","value"])):(i(),s(m,{key:1},[y(V,{placeholder:"开始值",value:e.beginValue,"onUpdate:value":l[18]||(l[18]=a=>e.beginValue=a),style:{width:"calc(50% - 15px)"}},null,8,["value"]),ye,y(V,{placeholder:"结束值",value:e.endValue,"onUpdate:value":l[19]||(l[19]=a=>e.endValue=a),style:{width:"calc(50% - 15px)"}},null,8,["value"])],64))],64))]),_:1},8,["labelCol"])}const cl=X(me,[["render",fe],["__scopeId","data-v-e62a9629"]]);export{cl as default};
