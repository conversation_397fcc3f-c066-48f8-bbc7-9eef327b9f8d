package org.jeecg.modules.crmfy.crmcommissioncalculation.service.impl;

import org.jeecg.modules.crmfy.crmcommissioncalculation.entity.CrmCommissionCalculation;
import org.jeecg.modules.crmfy.crmcommissioncalculation.mapper.CrmCommissionCalculationMapper;
import org.jeecg.modules.crmfy.crmcommissioncalculation.service.ICrmCommissionCalculationService;
import org.jeecg.modules.crmfy.crmcommissioncalculation.vo.CrmCommissionCalculationQueryVO;
import org.jeecg.modules.crmfy.crmcommissioncalculation.vo.CrmCommissionCalculationQueryPO;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

/**
 * @Description: 佣金计算表
 * @Author: jeecg-boot
 * @Date:   2025-04-11
 * @Version: V1.0
 */
@Service
public class CrmCommissionCalculationServiceImpl extends ServiceImpl<CrmCommissionCalculationMapper, CrmCommissionCalculation> implements ICrmCommissionCalculationService {

    @Override
    public IPage<CrmCommissionCalculationQueryVO> queryCommissionCalculationInfo(Page<CrmCommissionCalculationQueryVO> page, CrmCommissionCalculationQueryPO queryPO) {
        return this.baseMapper.queryCommissionCalculationInfo(page, queryPO);
    }

}
