import{aW as l}from"./vue-vendor-CnsIXr4W.js";import{cz as f,cA as m,cB as p,cC as g,cm as S}from"./index-BnxvmHFv.js";const x={vue:l,"@":{hooks:{useMessage:g,useUserStore:p},utils:{axios:m,cache:f}}};function _(u,s){const i=Object.assign({},x,u);function c(t){if(t!=null&&t!=""){let e=t.toString().split("/"),o=i[e[0]];for(let n=1;n<e.length;n++)o=o[e[n]];return o}return null}function r(){}function a(t,e){let o="__export_"+S(6);if(e){const n=`return function (row, customImport, ${o}) {"use strict"; ${t}}`;new Function(n)().call(s,e,c,r)}else{const n=`return function (customImport, ${o}) {"use strict"; ${t}}`;new Function(n)().call(s,c,r)}}return{executeJsEnhanced:a}}const w=/(?:\/\*[\s\S]*?\*\/|\/\/.*?\r?\n|[^{])+\{([\s\S]*)\}$/;export{_ as I,w as g};
