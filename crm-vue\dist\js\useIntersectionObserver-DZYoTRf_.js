import{f as c,h as a}from"./vue-vendor-CnsIXr4W.js";function i({target:n,root:r,onIntersect:l,rootMargin:o="0px",threshold:t=.1}){let u=()=>{};const e=c(null),v=a(()=>{u(),e.value=new IntersectionObserver(l,{root:r?r.value:null,rootMargin:o,threshold:t});const s=n.value;s&&e.value.observe(s),u=()=>{e.value&&(e.value.disconnect(),n.value&&e.value.unobserve(n.value))}});return{observer:e,stop:()=>{u(),v()}}}export{i as useIntersectionObserver};
