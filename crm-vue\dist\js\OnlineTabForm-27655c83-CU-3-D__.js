import{cl as St,u as Ct,ah as Ot,e as Z,cv as Pt,b8 as Rt,cr as Vt,aa as xt,bl as jt}from"./index-BnxvmHFv.js";import{f,r as Dt,u as ye,n as ve,J as x,e as At,w as Mt,aU as _t,aV as Bt,at as oe,ag as v,aq as J,ar as k,as as It,k as S,aG as Nt,aD as j,aB as H,ah as ee,F as $t,aC as Et,aA as Kt}from"./vue-vendor-CnsIXr4W.js";import"./index-D4YmpXsT.js";import{d as Jt,e as Ht,u as Ut,l as U,g as qt,V as I,S as Lt,f as Wt,h as Fe,O as zt,c as Qt}from"./useExtendComponent-dcae5c78-C--8iMn7.js";import{p as Gt,o as we,aw as Xt,bz as Yt,bA as Zt}from"./antd-vue-vendor-ac69AFxs.js";import"./index-j6u-UpGU.js";import{I as eo,g as to}from"./useCustomHook-acb00837-D2zAXJsh.js";import"./componentMap-Bg13cs1o.js";import"./constant-fa63bd66-Ddbq-fz2.js";import"./index-C9Hr1YQz.js";import"./user.api-_jc-Tnyz.js";import"./customExpression-CtXxD800.js";import"./index-DjTWLsmR.js";import"./useListPage-BCVdKj1d.js";import"./LinkTableListPiece-f3a8e0d7-BR0-U9EN.js";import"./OnlineSelectCascade-05c40fef-PZGR6IPg.js";import"./JModalTip-b055ab60-Co9Wp31o.js";import{u as oo,B as lo}from"./useForm-DNLebgae.js";import"./vxe-table-vendor-CK0mysZD.js";import"./JAreaLinkage-BDz8pfno.js";import"./areaDataUtil-ChdaTOUz.js";import"./JSelectUser-Blw_A8N4.js";import"./props-DHLM7ils.js";import"./JSelectBiz-rbVGd4Dv.js";import"./JSelectDept-1TdcISOX.js";import"./index-Dr3sBsWR.js";import"./index-BNka1IZr.js";import"./index-DmtYkh1u.js";import"./index-B4xK7O2u.js";import"./useTimeout-DoPujGLn.js";import"./useIntersectionObserver-DZYoTRf_.js";import"./bem-DfjdT9o5.js";import"./props-WP5sspMu.js";import"./useContextMenu-DwGGVBbH.js";import"./JCodeEditor-C8R0_wo6.js";import"./useFormItem-Eoh0MXq5.js";import"./htmlmixed-XbbCJteX.js";import"./vue-BLlfMlNv.js";/* empty css             */import"./JAddInput-B7aUDeJi.js";import"./index-86WEzeSw.js";import"./depart.api-BTFSMQ51.js";import"./JPopup-gJ1mXmQZ.js";import"./EasyCronInput-BB8IcbzA.js";import"./JEllipsis-IqMnhApY.js";import"./JUpload-DCci6z5o.js";import"./BasicTable-DO8Vk_Gm.js";import"./injectionKey-DPVn4AgL.js";import"./useWindowSizeFn-BcaCTZPc.js";import"./BasicModal-B-mPgnfO.js";import"./download-ZmOT1Dx2.js";import"./base64Conver-24EVOS6V.js";import"./index-DBQnQ9LG.js";import"./index-CUrp7h3O.js";import"./useCountdown-D_p0DKZc.js";import"./useFormItemSingle-D8EqzImO.js";import"./index-C3YRp4LI.js";import"./index-mK2t00gc.js";var ao=Object.defineProperty,no=Object.defineProperties,ro=Object.getOwnPropertyDescriptors,Te=Object.getOwnPropertySymbols,io=Object.prototype.hasOwnProperty,so=Object.prototype.propertyIsEnumerable,ke=(r,d,i)=>d in r?ao(r,d,{enumerable:!0,configurable:!0,writable:!0,value:i}):r[d]=i,D=(r,d)=>{for(var i in d||(d={}))io.call(d,i)&&ke(r,i,d[i]);if(Te)for(var i of Te(d))so.call(d,i)&&ke(r,i,d[i]);return r},uo=(r,d)=>no(r,ro(d)),C=(r,d,i)=>new Promise((n,V)=>{var F=b=>{try{y(i.next(b))}catch(h){V(h)}},O=b=>{try{y(i.throw(b))}catch(h){V(h)}},y=b=>b.done?n(b.value):Promise.resolve(b.value).then(F,O);y((i=i.apply(r,d)).next())});const te={optPre:"/online/cgform/api/form/",urlButtonAction:"/online/cgform/api/doButton"},co={name:"OnlineTabForm",components:{BasicForm:lo,Loading:jt,OnlineSubForm:Qt,PrinterOutlined:Zt,DiffOutlined:Yt,FormOutlined:Xt,OnlinePopModal:zt},props:{id:{type:String,default:""},formTemplate:{type:Number,default:1},disabled:{type:Boolean,default:!1},isTree:{type:Boolean,default:!1},pidField:{type:String,default:""},submitTip:{type:Boolean,default:!0},modalClass:{type:String,default:""},themeTemplate:{type:String,default:""},tabIndex:{type:String,default:""}},emits:["success","rendered","toggleTab"],setup(r,{emit:d}){const{createMessage:i}=Ct(),n=f(null),V=f(!0),F=f(!1),O=f(1),y=f(""),b=f(!1),h=f(!1),{getIsMobile:N}=Ot(),q=f(!N.value),P=Dt({reportPrintShow:0,reportPrintUrl:"",joinQuery:0,modelFullscreen:0,modalMinWidth:"",commentStatus:0}),{onlineFormContext:c,resetContext:L}=Jt(),{formSchemas:A,defaultValueFields:s,changeDataIfArray2String:$,tableName:m,dbData:w,checkOnlyFieldValue:Se,hasSubTable:Ce,subTabInfo:M,refMap:W,subDataSource:E,baseColProps:Oe,createFormSchemas:Pe,fieldDisplayStatus:_,labelCol:Re,wrapperCol:Ve,labelWidth:xe}=Ht(r,n);let{EnhanceJS:u,initCgEnhanceJs:je}=Ut(c,!1);const{executeJsEnhanced:De}=eo({},c),[Ae,{setProps:Me,validate:le,resetFields:ae,clearValidate:_e,setFieldsValue:T,updateSchema:z,getFieldsValue:Q,scrollToField:ne}]=oo({schemas:A,showActionButtonGroup:!1,baseColProps:Oe,labelWidth:xe,labelCol:Re,wrapperCol:Ve}),re=f(!1);function Be(){let e=r.disabled;re.value=e,Me({disabled:e})}function Ie(e,t,o){return C(this,null,function*(){yield Ne(),y.value="",yield ae(),setTimeout(()=>{_e()},0),w.value="";let l=ye(e);h.value=l,l?yield se(t):ue(),ve(()=>{!l&&o&&T(o),$e(),G("js","loaded"),Be()})})}function Ne(){return C(this,null,function*(){if(r.isTree===!0){let e=r.pidField,t=A.value;t&&t.length>0&&t.filter(o=>o.field===e).length>0&&(yield z({field:e,componentProps:{reload:new Date().getTime(),hiddenNodeKey:""}}))}})}function $e(){if(ye(h)===!1){let e=x(s[m.value]);U(e,t=>{T(t)})}}function ie(e,t){let o=x(s[e.key]);U(o,l=>{const{row:a,target:p}=t;let R=[{rowKey:a.id,values:D({},l)}];p.setValues(R)})}function se(e){return C(this,null,function*(){let t=yield Ke(e.id);w.value=Object.assign({},e,t);let o=Ee.value,l=Gt(t,...o);r.disabled&&Object.keys(l).map(a=>{!l[a]&&l[a]!==0&&l[a]!=="0"&&delete l[a]}),yield T(l),ue(t)})}function ue(e){e||(e={});let t=Object.keys(E.value);if(t&&t.length>0){let o={};for(let l of t)o[l]=e[l]||[];E.value=o}}let Ee=At(()=>{let e=A.value,t=[];for(let o of e)t.push(o.field);return t});function Ke(e){let t=`${te.optPre}${r.id}/${e}`;return new Promise((o,l)=>{Z.get({url:t},{isTransformResponse:!1}).then(a=>{a.success?o(a.result):(l(),i.warning(a.message))}).catch(()=>{l()})})}function Je(e){return C(this,null,function*(){O.value=e.head.tableType,m.value=e.head.tableName,V.value=e.head.tableType==1,qe(e.head.extConfigJson),Pe(e.schema.properties,e.schema.required,Se,P),u=je(e.enhanceJs),d("rendered",P);let t=yield qt(n);t.$formValueChange=(o,l,a)=>{st(o,l),a&&T(a),He(o,l,a)},u&&u.setup&&pe(u.setup)})}function He(e,t,o){c.changEvent(e,t,o)}function Ue(e){c.addObject2Context("changEvent",e)}function qe(e){let t={reportPrintShow:0,reportPrintUrl:"",joinQuery:0,modelFullscreen:0,modalMinWidth:"",commentStatus:0,formLabelLength:null};e&&(t=JSON.parse(e)),Object.keys(t).map(o=>{P[o]=t[o]})}function Le(){V.value===!0?Xe():We()}function We(){ze().then(e=>{de(e)})}function ze(){let e={};return new Promise((t,o)=>{le().then(l=>t(l),({errorFields:l})=>{o({code:I,key:m.value,scrollToField:()=>l[0]&&ne(l[0].name,{behavior:"smooth",block:"center"})})})}).then(t=>(Object.assign(e,$(t)),Ge())).then(t=>(Object.assign(e,t),Promise.resolve(e))).catch(t=>((t===I||(t==null?void 0:t.code)===I)&&(i.warning("校验未通过"),t.key&&(Qe(t.key),t.scrollToField&&setTimeout(()=>t.scrollToField(),150))),Promise.reject(null)))}function Qe(e){if(e===m.value){d("toggleTab","-1");return}let t=M.value;for(let o=0;o<t.length;o++)if(e==t[o].key){let l=o+"";if(K.value===l)break;if(d("toggleTab",l),t[o].relationType===0){let a=g(e);Pt(300,()=>a==null?void 0:a.validateTable())}break}}function Ge(){return new Promise((e,t)=>C(this,null,function*(){let o={};try{let l=M.value;for(let a=0;a<l.length;a++){let p=l[a].key,R=g(p);if(l[a].relationType==1)try{let B=yield R.getAll();o[p]=[],o[p].push(B)}catch(B){return t(D({code:I,key:p},B))}else{if(yield R.fullValidateTable())return t({code:I,key:p});o[p]=R.getTableData()}}}catch(l){t(l)}e(o)}))}function Xe(){return C(this,null,function*(){try{let e=yield le();e=Object.assign({},w.value,e),e=$(e),F.value=!0,de(e)}catch(e){Array.isArray(e==null?void 0:e.errorFields)&&e.errorFields[0]&&ne(e.errorFields[0].name,{behavior:"smooth",block:"center"})}finally{F.value=!1}})}function de(e){ct(ge,e).then(()=>{Ye(e)}).catch(t=>{i.warning(t)})}function Ye(e){Object.keys(e).map(a=>{Array.isArray(e[a])&&e[a].length==0&&(e[a]="")});let t=y.value,o=`${te.optPre}${r.id}?tabletype=${O.value}`;t&&(o=`${t}?tabletype=${O.value}`),b.value===!0&&(e[Lt]=1);let l=h.value===!0?"put":"post";Z.request({url:o,method:l,params:e},{isTransformResponse:!1}).then(a=>{a.success?(a.result&&(e[Wt]=a.result),d("success",e),r.submitTip===!0&&i.success(a.message)):i.warning(a.message)}).finally(()=>{F.value=!1})}function Ze(e,t,o){t&&o?o.vxeProps?o.setValues([{rowKey:t,values:e}]):o.setValues(e):T(e)}function et(e,t){let o={};o[e]=t,T(o)}const K=f("0"),ce=f("auto"),me=f(340);function tt(e){if(h.value===!0){let t=w.value;return ot(t,e)}return""}Mt(()=>r.tabIndex,e=>{K.value=e},{immediate:!0});function ot(e,t){if(e){let o=e[t];return!o&&o!==0&&(o=e[t.toLowerCase()],!o&&o!==0&&(o=e[t.toUpperCase()])),o}return""}function lt(e,t){if(u&&u[t+"_onlChange"]){let o=u[t+"_onlChange"](),l=Object.keys(e)[0];if(o[l]){let a=g(t).getFormEvent(),p=D({column:{key:l},value:e[l]},a);o[l].call(c,c,p)}}}function at(e,t){if(u&&u[t+"_onlChange"]){let o=u[t+"_onlChange"](c);if(e.column==="all"){let l=Object.keys(o);if(l.length>0)for(let a of l)o[a].call(c,c,e)}else{let l=e.column.key||e.col.key;o[l]&&e.row&&e.row.id&&o[l].call(c,c,e)}}}function nt(e,t){var o;if(u&&u[t+"_onlChange"]){let l=u[t+"_onlChange"](c),a=Object.keys(l);if(a.length>0)for(let p of a)(o=l[p])==null||o.call(c,c,uo(D({},e),{row:e.deleteRows}))}}function rt(e,t){t.isModalData||ie(e,t)}function it(e){return"online_"+e+":"}function st(e,t){return C(this,null,function*(){if(!u||!u.onlChange||!e)return!1;let o=u.onlChange();if(o[e]){let l={row:yield Q(),column:{key:e},value:t};o[e].call(c,c,l)}})}function pe(e){let t=e.toLocaleString().match(to);if(t.length>1){let o=t[1];De(o)}}function G(e,t){if(e=="js"){let o=t+"_hook";u&&u[t]?u[t].call(c,c):u&&u[o]&&pe(u[o])}else if(e=="action"){let o=w.value,l={formId:r.id,buttonCode:t,dataId:o.id,uiFormData:Object.assign({},o)};Z.post({url:`${te.urlButtonAction}`,params:l},{isTransformResponse:!1}).then(a=>{a.success?i.success("处理完成!"):i.warning("处理失败!")})}}function fe(e){let t=g(e),o=[...t.getNewDataWithId(),...E.value[e]];if(!o||o.length==0)return!1;let l=[];for(let a of o)l.push(a.id);t.removeRowsById(l)}function he(e,t){if(!t)return!1;let o=g(e);typeof t=="object"?o.addRows(t,!0):this.$message.error("添加子表数据,参数不识别!")}function ut(e,t){fe(e),he(e,t)}function dt(e,t){!t&&t.length<=0&&(t=[]),t.map(o=>{o.hasOwnProperty("label")||(o.label=o.text)}),z({field:e,componentProps:{options:t}})}function ct(e,t){return u&&u.beforeSubmit?u.beforeSubmit(e,t):Promise.resolve()}function mt(e,t){let o=x(_);e&&e.length>0?Object.keys(o).map(l=>{!l.endsWith("_load")&&e.indexOf(l)<0&&(_[l]=!1)}):t&&t.length>0&&Object.keys(o).map(l=>{t.indexOf(l)>=0&&(_[l]=!1)})}function pt(e,t){return C(this,null,function*(){y.value=t,yield ae(),w.value="",h.value=!0,yield se(e),yield ve(()=>{G("js","loaded")})})}function g(e){let t=W[e].value;if(t instanceof Array&&(t=t[0]),!t){i.warning("子表ref找不到:"+e);return}return t}function ft(){let e=P.reportPrintUrl,t=w.value.id,o=Rt();Vt(e,t,o)}const[ht,{openModal:be}]=xt(),X=f(""),Y=f(!0);function bt(e){X.value=e.id,Y.value=!1,be(!0,{isUpdate:!1,tableType:"3"})}function gt(e){let t=g(e.key).getSelectedData();if(t.length!=1){i.warning("请选择一条数据");return}X.value=e.id,Y.value=!1,be(!0,{isUpdate:!0,record:t[0]})}function yt(e){const t=e[Fe];let o=we(e,[Fe]);if(o.id){let l=we(D({},o),"id"),a=[{rowKey:o.id,values:l}];g(t).setValues(a)}else g(t).addRows(o,{isOnlineJS:!1,setActive:!1,emitChange:!0,isModalData:!0})}function vt(){let e=M.value;if(e&&e.length>0){for(let t of e)if(t.relationType!=1){let o=g(t.key);o&&o.clearSelection()}}}function Ft(){let e=Q(),t=x(s[m.value]);U(t,o=>{T(o)},e)}function wt(e,t){let o=M.value;if(o&&o.length>0){let l=o.filter(a=>a.key===e);if(l.length==0)return;if(l[0].relationType==1)g(e).executeFillRule();else{let a=x(s[e]),p=x(t.row);U(a,R=>{const{row:B,target:Tt}=t;let kt=[{rowKey:B.id,values:D({},R)}];Tt.setValues(kt)},p)}}}let ge={tableName:m,loading:F,subActiveKey:K,onlineFormRef:n,getFieldsValue:Q,setFieldsValue:T,submitFlowFlag:b,subFormHeight:ce,subTableHeight:me,refMap:W,triggleChangeValues:Ze,triggleChangeValue:et,sh:_,clearSubRows:fe,addSubRows:he,clearThenAddRows:ut,changeOptions:dt,isUpdate:h,getSubTableInstance:g,updateSchema:z,executeMainFillRule:Ft,executeSubFillRule:wt,changEvent:()=>{},onlineFormValueChange:Ue};return L(ge),{tableName:m,onlineFormRef:n,registerForm:Ae,loading:F,subActiveKey:K,hasSubTable:Ce,subTabInfo:M,refMap:W,subFormHeight:ce,getSubTableForeignKeyValue:tt,isUpdate:h,handleSubFormChange:lt,subTableHeight:me,onlineFormDisabled:re,subDataSource:E,getSubTableAuthPre:it,handleAdded:rt,handleSubTableDefaultValue:ie,handleValueChange:at,openSubFormModalForAdd:bt,openSubFormModalForEdit:gt,handleRemoved:nt,show:Ie,createRootProperties:Je,handleSubmit:Le,sh:_,handleCgButtonClick:G,handleCustomFormSh:mt,handleCustomFormEdit:pt,dbData:w,onOpenReportPrint:ft,onlineExtConfigJson:P,registerPopModal:ht,popTableName:X,getPopFormData:yt,popModalRequest:Y,onCloseModal:vt,rowNumber:q}}},mo=r=>(_t("data-v-dec72f46"),r=r(),Bt(),r),po=["id"],fo={key:1},ho={class:"add"},bo=mo(()=>oe("svg",{t:"1714390025804",class:"icon",viewBox:"0 0 1024 1024",version:"1.1",xmlns:"http://www.w3.org/2000/svg","p-id":"50960",width:"16",height:"16"},[oe("path",{d:"M469.333333 469.333333V170.666667h85.333334v298.666666h298.666666v85.333334h-298.666666v298.666666h-85.333334v-298.666666H170.666667v-85.333334h298.666666z",fill:"#fff","p-id":"50961"})],-1));function go(r,d,i,n,V,F){const O=v("BasicForm"),y=v("a-tab-pane"),b=v("online-sub-form"),h=v("diff-outlined"),N=v("a-button"),q=v("form-outlined"),P=v("JVxeTable"),c=v("a-tabs"),L=v("Loading"),A=v("online-pop-modal");return k(),J("div",{id:n.tableName+"_form",class:It(["onlineFormWrap",[`formTemplate_${i.formTemplate}`]])},[S(c,{class:"tabTheme",activeKey:n.subActiveKey,"onUpdate:activeKey":d[0]||(d[0]=s=>n.subActiveKey=s)},{default:j(()=>[(k(),H(y,{tab:"主表",key:"-1"},{default:j(()=>[S(O,{ref:"onlineFormRef",onRegister:n.registerForm},null,8,["onRegister"])]),_:1})),n.hasSubTable?(k(!0),J($t,{key:0},Et(n.subTabInfo,(s,$)=>(k(),H(y,{tab:s.describe,key:$+"",forceRender:!0},{default:j(()=>[s.relationType==1?(k(),J("div",{key:0,style:Kt({"overflow-y":"auto","overflow-x":"hidden","max-height":n.subFormHeight+"px"})},[S(b,{ref_for:!0,ref:n.refMap[s.key],table:s.key,disabled:n.onlineFormDisabled,"form-template":i.formTemplate,"main-id":n.getSubTableForeignKeyValue(s.foreignKey),properties:s.properties,"required-fields":s.requiredFields,"is-update":n.isUpdate,onFormChange:m=>n.handleSubFormChange(m,s.key)},null,8,["table","disabled","form-template","main-id","properties","required-fields","is-update","onFormChange"])],4)):(k(),J("div",fo,[S(P,{ref_for:!0,ref:n.refMap[s.key],toolbar:"","keep-source":"","row-number":n.rowNumber,"row-selection":"",height:n.subTableHeight,disabled:n.onlineFormDisabled,columns:s.columns,dataSource:n.subDataSource[s.key],onValueChange:m=>n.handleValueChange(m,s.key),onRemoved:m=>n.handleRemoved(m,s.key),authPre:n.getSubTableAuthPre(s.key),onAdded:m=>n.handleAdded(s,m),onExecuteFillRule:m=>n.handleSubTableDefaultValue(s,m)},{toolbarSuffix:j(()=>[n.onlineFormDisabled?ee("",!0):(k(),H(N,{key:0,type:"primary",onClick:m=>n.openSubFormModalForAdd(s)},{default:j(()=>[oe("div",ho,[bo,S(h)])]),_:2},1032,["onClick"])),n.onlineFormDisabled?ee("",!0):(k(),H(N,{key:1,type:"primary",onClick:m=>n.openSubFormModalForEdit(s)},{default:j(()=>[S(q)]),_:2},1032,["onClick"]))]),_:2},1032,["row-number","height","disabled","columns","dataSource","onValueChange","onRemoved","authPre","onAdded","onExecuteFillRule"])]))]),_:2},1032,["tab"]))),128)):ee("",!0)]),_:1},8,["activeKey"]),S(L,{loading:n.loading,absolute:!1},null,8,["loading"]),Nt(r.$slots,"bottom",{},void 0,!0),S(A,{formTableType:"3",request:n.popModalRequest,id:n.popTableName,onRegister:n.registerPopModal,onSuccess:n.getPopFormData,topTip:"",isVxeTableData:""},null,8,["request","id","onRegister","onSuccess"])],10,po)}const Tl=St(co,[["render",go],["__scopeId","data-v-dec72f46"]]);export{Tl as default};
