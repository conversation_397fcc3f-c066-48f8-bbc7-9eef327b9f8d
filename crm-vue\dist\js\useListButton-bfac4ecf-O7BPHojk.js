import{E as Y,r as De}from"./useExtendComponent-dcae5c78-C--8iMn7.js";import{f as v,p as Pe,aN as Oe,J as R,j as ke,n as ge,r as de}from"./vue-vendor-CnsIXr4W.js";import{u as Se,c0 as xe,R as Fe,bh as he,e as O,E as we,aa as Q,b8 as Re,cr as Ee,C as $e}from"./index-BnxvmHFv.js";import{g as Ie,I as Me}from"./useCustomHook-acb00837-D2zAXJsh.js";import"./index-j6u-UpGU.js";import{s as W}from"./constant-fa63bd66-Ddbq-fz2.js";import{h as je}from"./cgformState-d9f8ec42-L9FLsj48.js";import{p as Ne,M as Ue}from"./antd-vue-vendor-ac69AFxs.js";var Be=Object.defineProperty,Ke=Object.defineProperties,Qe=Object.getOwnPropertyDescriptors,be=Object.getOwnPropertySymbols,ze=Object.prototype.hasOwnProperty,Je=Object.prototype.propertyIsEnumerable,ye=(r,c,p)=>c in r?Be(r,c,{enumerable:!0,configurable:!0,writable:!0,value:p}):r[c]=p,F=(r,c)=>{for(var p in c||(c={}))ze.call(c,p)&&ye(r,p,c[p]);if(be)for(var p of be(c))Je.call(c,p)&&ye(r,p,c[p]);return r},fe=(r,c)=>Ke(r,Qe(c)),Z=(r,c,p)=>new Promise((d,C)=>{var T=m=>{try{u(p.next(m))}catch(y){C(y)}},D=m=>{try{u(p.throw(m))}catch(y){C(y)}},u=m=>m.done?d(m.value):Promise.resolve(m.value).then(T,D);u((p=p.apply(r,c)).next())});const _e={acceptHrefParams:"<p> 跳转时获取的参数信息",currentPage:"<p> 当前页数",currentTableName:"<p> 当前表名",description:"<p> 当前表描述",hasChildrenField:"<p> 是否有子节点的字段名，仅树形表单下有效",isDesForm:"<p> xx",isTree:"<m> 是否是树形表单 ",loadData:"<m> 加载列表数据",pageSize:"<p> 每一页显示条数",queryParam:"<p> 查询条件对象，每次点击查询后才会更新此数据",selectedRowKeys:"<p> 选中的行的id数组",sortField:"<p> 排序字段",sortType:"<p> 排序规则",total:"<p> 总页数",foreignKeyValue:"<p> Erp一对多子表外键选中对应主表字段的值",isErpSubTable:"<p> 是否Erp一对多子表",foreignKeyField:"<p> Erp一对多子表外键字段",themeTemplate:"<p> 主题模板",isInnerSubTable:"<p> 是否内嵌一对多子表",innerSubTableId:"<p>内嵌一对多子表ID",innerSubTableName:"<p> 内嵌一对多子表名",mTableSelectedRcordId:"<p>内嵌主表展开行的id",innerSubTableFk:"<p>内嵌子表的外键字段"},Le={getColumns:"/online/cgform/api/getColumns/",getQueryInfo:"/online/cgform/api/getQueryInfo/",getData:"/online/cgform/api/getData/",getTreeData:"/online/cgform/api/getTreeData/",optPre:"/online/cgform/api/form/",buttonAction:"/online/cgform/api/doButton",exportXls:"/online/cgform/api/exportXlsOld/",importXls:"/online/cgform/api/importXls/",startProcess:"/act/process/extActProcess/startMutilProcess",getErpColumns:"/online/cgform/api/getErpColumns/",list:"/online/cgform/api/subform/list/"};let Ae={sortField:"id",sortType:"asc",currentPage:1,pageSize:10,total:0,selectedRowKeys:[],queryParam:{},acceptHrefParams:{},description:"",currentTableName:"",isDesForm:!1,desFormCode:"",cache:!1,isTree:!1,hasChildrenField:""};const ve={current:1,pageSize:10,pageSizeOptions:["10","20","30"],showTotal:(r,c)=>c[0]+"-"+c[1]+" 共"+r+"条",showQuickJumper:!0,showSizeChanger:!0,total:0},{createMessage:K,createErrorModal:He}=Se();function tt(r={}){var c;const p=(c=r.code)!=null?c:"",d=v(p);Pe("tableId",d);const C=Oe(),T=v(),D=v(),u=v(!1),m=v([]),y=v(!0),z=v(),J=je(),h=xe();let g={};const S={execButtonEnhance:function(e,t){if(o[Y][e])if(De===e)P(e);else{let n=R(t);return o[Y][e].call(o,o,n)}else if(o[Y][e+"_hook"])if(t){let n=R(t);P(e+"_hook",n)}else P(e+"_hook")},isTree:function(e){return typeof e=="boolean"?(o.isTreeTable=e,e):o.isTreeTable}};function P(e,t){let n=o[Y][e].toLocaleString().match(Ie);if(n.length>1){let l=n[1];G(l,t)}}const o=new Proxy(_e,{get(e,t){if(typeof S[t]=="function")return S[t];{let n=g[d.value];return Reflect.get(n,t)}},set(e,t,n){let l=_();return Reflect.set(typeof n=="function"?S:l,t,n)},deleteProperty(e,t){return t===d.value?(delete g[t],!0):!1}}),{executeJsEnhanced:G}=Me({},o);function ee(){let e=C.params.id;return e||(e=""),e}Fe(({type:e})=>{!p&&q(),e==="activated"&&J.checkIsChanged(d.value)&&h.refreshPage(he),d.value&&J.removeChangedTable(d.value)}),ke(()=>{delete g[d.value]});function _(){let e=g[d.value];if(!e){let t=Object.assign({},Ae,{onlineUrl:Le});e=JSON.parse(JSON.stringify(t)),r.themeTemplate==W&&(e.pageSize=5),g[d.value]=e}return e}function L(){let e={},t=C.query;t&&(Object.keys(t).map(n=>{e[n]=t[n]}),o.acceptHrefParams=e)}function te(e=""){let t;return e==W?t=`${o.onlineUrl.getErpColumns}${d.value}`:t=`${o.onlineUrl.getColumns}${d.value}`,new Promise((n,l)=>{O.get({url:t},{isTransformResponse:!1}).then(a=>{a.success?n(a.result):(K.warning(a.message),l())}).catch(()=>{l()})})}function k(e={}){const{delNum:t}=e;return new Promise((n,l)=>{if(t!=null){const{total:s,pageSize:b,current:f}=u.value,N=Math.ceil(s/b);f===N&&(u.value.current=Math.ceil((s-t)/b))}let a=E(),i=`${o.onlineUrl.getData}${d.value}`;o.isTree()===!0?i=`${o.onlineUrl.getTreeData}${d.value}`:o.isInnerSubTable===!0&&(i=`${o.onlineUrl.getData}${o.innerSubTableId}`,a={pageSize:-521},o.innerSubTableFk&&o.mTableSelectedRcordId&&(a[o.innerSubTableFk]=o.mTableSelectedRcordId)),o.isErpSubTable===!0&&(a[o.foreignKeyField]=o.foreignKeyValue,a.tabletype=3,delete a.hasQuery),O.get({url:i,params:a},{isTransformResponse:!1}).then(s=>{s.success?(ne(s.result),n(!0)):(s.message==="NO_DB_SYNC"?He({title:"数据库未同步",content:"请先同步数据库再查看此页面！",onOk:()=>he.back()}):K.warning(s.message),l(!1))}).catch(()=>{K.warning("请求列表数据异常!"),l(!1)})})}function E(){const{sortField:e,sortType:t,acceptHrefParams:n,queryParam:l}=o;let a={};o.isTree(),a.hasQuery="true";let i=Object.assign({},a,n,l,{column:e,order:t});u.value?(i.pageNo=u.value.current,i.pageSize=u.value.pageSize):i.pageSize=-521;let s=ie();return i.superQueryMatchType=s.matchType||"",i.superQueryParams=s.params||"",we(i)}function ne(e){let t=0;Number(e.total)>0?(o.isTree()===!0?(m.value=x(e.records),ge(()=>{ae(m.value)})):m.value=e.records,t=Number(e.total)):m.value=[],u.value&&(u.value=fe(F({},u.value),{total:t}))}function re(e,t,n){n&&n.order?(o.sortField=n.field,o.sortType=n.order=="ascend"?"asc":"desc"):(o.sortField="id",o.sortType="asc"),u.value&&(u.value=e),k()}function le(e){o.description=e.description,o.currentTableName=e.currentTableName,o.isDesForm=e.isDesForm,o.desFormCode=e.desFormCode,o.ID=d.value;let{acceptHrefParams:t,queryParam:n,superQuery:l,currentPage:a,pageSize:i}=o;if(L(),n?T.value&&T.value.initDefaultValues(n,t):o.queryParam={},l?D.value&&D.value.initDefaultValues(l):o.superQuery={params:"",matchType:""},e.paginationFlag=="Y"){let s=ve.pageSizeOptions;r.themeTemplate==W&&(s=["5","10","30"]),u.value=fe(F({},ve),{current:a,pageSize:i,pageSizeOptions:s})}else u.value=!1}function $(){return Z(this,null,function*(){y.value=!0,yield ge(),y.value=!1})}const I={loadData:k,getLoadDataParams:E,reloadTable:$};Object.keys(I).map(e=>{o[e]=I[e]});let oe=v(!1);function A(){return Z(this,arguments,function*(e={}){u.value&&(u.value=fe(F({},u.value),{current:e.mode=="search"||!u.value.current?1:u.value.current})),r.themeTemplate!==W&&o.clearSelectedRow(),yield k()})}function x(e){if(e)return e.map(t=>{let n=o.hasChildrenField;if(t[n]=="1"){let l={id:t.id+"_loadChild",name:"loading...",isLoading:!0};l.jeecg_row_key=l.id,t.children=[l]}return t})}const M=v([]);function H(e){M.value=e}function ae(e){let t=M.value;if(t.length>0){const{sortField:n,sortType:l,pidField:a}=o;let i=Object.assign({},{column:n,order:l});i.hasQuery="in";let s=Object.assign({});s.rule="in",s.type="text",s.val=t.join(","),s.field=a,s=[s],i.superQueryParams=encodeURI(JSON.stringify(s)),i.superQueryMatchType="and",i.batchFlag="true";let b=`${o.onlineUrl.getTreeData}${d.value}`;O.get({url:b,params:i},{isTransformResponse:!1}).then(f=>{if(f.success&&f.result.records&&f.result.records.length>0){let N=f.result.records;const U=new Map;for(let B of N){let w=B[a];if(t.join(",").includes(w)){let X=U.get(w);X==null&&(X=[]),X.push(B),U.set(w,X)}}let Ce=U,me=B=>{B&&B.forEach(w=>{t.includes(w.id)&&(w.children=x(Ce.get(w.id)),me(w.children))})};me(e)}}).catch(()=>{K.warning("loadDataByExpandedRows请求列表数据异常!")})}else return Promise.resolve()}function ie(){if(!o.superQuery)return{};const{superQuery:{params:e,matchType:t},currentTableName:n}=o;let l=n+"@",a=[];if(e.length>0)for(let s of e){let b=F({},s),f=b.field;f.startsWith(l)&&(b.field=f.replace(l,"")),a.push(b)}let i=a.length>0?JSON.stringify(a):"";return{params:encodeURIComponent(i),matchType:t}}const j=v(!1);function se(e,t){o.superQuery={params:e,matchType:t},e.length==0||e.length==null?j.value=!1:j.value=!0,u.value.current=1,k()}const[ue,{openModal:ce}]=Q();function pe(e){if(e||(e={}),!e.row){let t=o.selectedRows;if(!t||t.length==0||t.length>1){K.warning("请选择一条数据");return}e.row=t[0]}e.code=d.value,ce(!0,e)}o.openCustomModal=pe;function q(){let e=ee();d.value=e}!p&&!d.value&&q();function V(e){let t=e.head.extConfigJson;t&&(z.value=JSON.parse(t))}return F({ID:d,onlineQueryFormOuter:T,superQueryButtonRef:D,loading:oe,reload:A,dataSource:m,pagination:u,tableReloading:y,handleSpecialConfig:le,onlineTableContext:o,handleChangeInTable:re,getColumnList:te,getTreeDataByResult:x,expandedRowKeys:M,handleExpandedRowsChange:H,onlineExtConfigJson:z,handleFormConfig:V,superQueryStatus:j,handleSuperQuery:se,registerCustomModal:ue},I)}const Te="onl_";function nt(r,c,p={}){const d={add:!0,addSub:!0,update:!0,delete:!0,batch_delete:!0,import:!0,export:!0,detail:!0,super_query:!0,bpm:!0},[C,{openModal:T}]=Q(),[D,{openModal:u}]=Q(),[m,{openModal:y}]=Q(),[z,{openModal:J}]=Q(),{createMessage:h}=Se(),g=de(d),S=de([]),P=de([]);function o(e){if(S.length=0,P.length=0,e&&e.length>0)for(let t=0;t<e.length;t++){let n=Ne(e[t],"buttonCode","buttonName","buttonStyle","optType","exp","buttonIcon");n.buttonStyle=="button"?P.push(n):n.buttonStyle=="link"&&S.push(n)}}function G(e){Object.keys(g).forEach(t=>{g[t]=!0}),e&&e.length>0&&Object.keys(g).forEach(t=>{e.indexOf(t)>=0&&(g[t]=!1)})}function ee(e){let t={isUpdate:!1};e&&(t.param=e),T(!0,t)}function _(e){r.beforeEdit(e).then(()=>{T(!0,{isUpdate:!0,record:e})}).catch(t=>{h.warning(t)})}const L=e=>({label:"删除",popConfirm:{title:"是否删除？",confirm:te.bind(null,e)}});function te(e){r.beforeDelete(e).then(()=>{x(e.id,!1)}).catch(t=>{h.warning(t)})}function k(e){let t=$(e),n=t&&(t=="1"||t=="3"||t=="4")||!t;return R(g.update)===!0&&n?[{label:"编辑",onClick:l=>{p.editClickCallback&&p.editClickCallback(e.id,l),_(e)}}]:[]}function E(e){return{label:"提交流程",popConfirm:{title:"确认提交流程吗？",confirm:A.bind(null,e)}}}function ne(e){return{label:"审批进度",onClick:re.bind(null,e)}}function re(e){const{currentTableName:t}=r;let n=t;t.includes("$")&&(n=t.split("$")[0]);let l=Te+n,a=e.id;J(!0,{flowCode:l,dataId:a})}function le(e,t={}){let n=[];if(R(g.detail)===!0&&n.push({label:"详情",onClick:I.bind(null,e)}),r.hasBpmStatus===!0&&R(g.bpm)===!0){let s=$(e);!s||s=="1"?n.push(E(e)):n.push(ne(e))}if(c.value){let{reportPrintShow:s,reportPrintUrl:b}=c.value;s&&b&&n.push({label:"打印",onClick(){let f=b,N=e.id,U=Re();Ee(f,N,U)}})}let l=$(e),a=l&&l=="1"||!l;R(g.delete)===!0&&a&&n.push(L(e));let i=S;if(i&&i.length>0)for(let s of i)q(s.exp||"",e)===!0&&n.push({label:s.buttonName,onClick:H.bind(null,e,s.buttonCode,s.optType)});return n}function $(e){const t="bpm_status";let n=e[t];return n||(n=e[t.toUpperCase()]),n}function I(e){y(!0,{isUpdate:!0,disableSubmit:!0,record:e})}function oe(e){const{currentTableName:t,onlineUrl:{startProcess:n}}=r;let l=t;t.includes("$")&&(l=t.split("$")[0]);let a={url:n,params:{flowCode:Te+l,id:e.id,formUrl:"modules/bpm/task/form/OnlineFormDetail",formUrlMobile:"check/onlineForm/detail"}},i={isTransformResponse:!1};return new Promise((s,b)=>{O.post(a,i).then(f=>{f.success?(s(f),h.success(f.message)):(b(),h.warning(f.message))})})}function A(e){return Z(this,null,function*(){yield oe(e),r.loadData()})}function x(e,t=!0){let n=`${r.onlineUrl.optPre}${r.ID}/${e}`;return r.isErpSubTable===!0&&(n=`${n}?tabletype=3`),new Promise((l,a)=>{O.delete({url:n},{isTransformResponse:!1}).then(i=>{i.success?(h.success(i.message),r.loadData({delNum:e.split(",").length}),t||p.singleDelCallback&&p.singleDelCallback(e),l(!0)):(h.warning(i.message),a())})})}function M(){let e=r.selectedRowKeys;if(e.length<=0)return h.warning("请选择一条记录！"),!1;{let t=[];e.forEach(function(l){let a=l;a&&a.endsWith("_loadChild")&&(a=a.replace("_loadChild","")),t.indexOf(a)<0&&t.push(a)});let n=t.join(",");Ue.confirm({title:"确认删除",content:"是否删除选中数据",okText:"确认",cancelText:"取消",onOk:()=>Z(this,null,function*(){yield x(n),r.clearSelectedRow()})})}}function H(e,t,n){if(n=="js")r.execButtonEnhance(t,e);else if(n=="action"){let l={formId:r.ID,buttonCode:t,dataId:e.id},a=`${r.onlineUrl.buttonAction}`;O.post({url:a,params:l},{isTransformResponse:!1}).then(i=>{i.success?(r.loadData(),h.success("处理完成!")):h.warning(i.message)})}}function ae(e){r.execButtonEnhance(e)}function ie(e){let t=r.selectedRowKeys;if(!t||t.length==0)return h.warning("请先选中一条记录"),!1;let n=t.join(","),l={formId:r.ID,buttonCode:e,dataId:n},a=`${r.onlineUrl.buttonAction}`;O.post({url:a,params:l},{isTransformResponse:!1}).then(i=>{i.success?(r.loadData(),r.clearSelectedRow(),h.success("处理完成!")):h.warning(i.message)})}function j(){r.foreignKeyField&&r.foreignKeyValue?u(!0,{[r.foreignKeyField]:r.foreignKeyValue}):u(!0)}const se=()=>{let e=`${r.onlineUrl.importXls}${r.ID}`;return r.isErpSubTable===!0&&(e=`${e}?tabletype=3`),e},{handleExportXlsx:ue}=$e();function ce(){let e=r.getLoadDataParams(),t=r.selectedRowKeys;t&&t.length>0&&(e.selections=t.join(","));let n={};r.isErpSubTable===!0&&(n={tabletype:3},r.foreignKeyField&&r.foreignKeyValue&&(e[r.foreignKeyField]=r.foreignKeyValue));let l=JSON.stringify(we(e)),a=`${r.onlineUrl.exportXls}${r.ID}`;const i=r.description;return ue(i,a,F({paramsStr:l},n))}function pe(e,t){const n=[];e.split("||").forEach(a=>{const i=[];a.trim().split("&&").forEach(s=>{i.push(V(s.trim(),t))}),n.push(i.join("&&"))});const l=n.join("||");return new Function(`return ${l}`)()}function q(e,t){return!e||e==""?!0:e.indexOf("||")==-1&&e.indexOf("&&")==-1?V(e,t):pe(e,t)}function V(e,t){if(!e||e=="")return!0;let n=e.split("#"),l=t[n[0]],a=n[1].toLowerCase();return a==="eq"?l==n[2]:a==="ne"?l!=n[2]:a==="empty"?n[2]==="true"?!l||l=="":l&&l.length>0:a==="in"?n[2].split(",").indexOf(String(l))>=0:!1}return{buttonSwitch:g,cgLinkButtonList:S,cgTopButtonList:P,importUrl:se,registerModal:C,handleAdd:ee,handleEdit:_,handleBatchDelete:M,registerImportModal:D,onImportExcel:j,onExportExcel:ce,getDropDownActions:le,getActions:k,cgButtonJsHandler:ae,cgButtonActionHandler:ie,cgButtonLinkHandler:H,initButtonList:o,initButtonSwitch:G,getDeleteButton:L,handleSubmitFlow:A,getSubmitFlowButton:E,registerDetailModal:m,registerBpmModal:z,openDetailModal:y}}export{nt as d,tt as p};
