import { defHttp } from '/@/utils/http/axios';
import { useMessage } from '/@/hooks/web/useMessage';

const { createConfirm } = useMessage();

enum Api {
  list = '/crmcommissioncalculation/crmCommissionCalculation/list',
  save = '/crmcommissioncalculation/crmCommissionCalculation/add',
  edit = '/crmcommissioncalculation/crmCommissionCalculation/edit',
  deleteOne = '/crmcommissioncalculation/crmCommissionCalculation/delete',
  deleteBatch = '/crmcommissioncalculation/crmCommissionCalculation/deleteBatch',
  importExcel = '/crmcommissioncalculation/crmCommissionCalculation/importExcel',
  exportXls = '/crmcommissioncalculation/crmCommissionCalculation/exportXls',
  queryCommissionInfo = '/crmcommissioncalculation/crmCommissionCalculation/queryCommissionInfo',
  exportCommissionInfoXls = '/crmcommissioncalculation/crmCommissionCalculation/exportCommissionInfoXls',
}

/**
 * 导出api
 */
export const getExportUrl = Api.exportXls;

/**
 * 导出佣金计算信息api
 */
export const getExportCommissionInfoUrl = Api.exportCommissionInfoXls;

/**
 * 导入api
 */
export const getImportUrl = Api.importExcel;

/**
 * 列表接口
 * @param params
 */
export const list = (params) => defHttp.get({ url: Api.list, params });

/**
 * 查询佣金计算信息接口
 * @param params
 */
export const queryCommissionInfo = (params) => defHttp.get({ url: Api.queryCommissionInfo, params });

/**
 * 删除单个
 */
export const deleteOne = (params, handleSuccess) => {
  return defHttp.delete({ url: Api.deleteOne, params }, { joinParamsToUrl: true }).then(() => {
    handleSuccess();
  });
};

/**
 * 批量删除
 */
export const batchDelete = (params, handleSuccess) => {
  createConfirm({
    iconType: 'warning',
    title: '确认删除',
    content: '是否删除选中数据',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp.delete({ url: Api.deleteBatch, data: params }, { joinParamsToUrl: true }).then(() => {
        handleSuccess();
      });
    }
  });
};

/**
 * 保存或者更新
 * @param params
 */
export const saveOrUpdate = (params, isUpdate) => {
  let url = isUpdate ? Api.edit : Api.save;
  return defHttp.post({ url: url, params });
};
