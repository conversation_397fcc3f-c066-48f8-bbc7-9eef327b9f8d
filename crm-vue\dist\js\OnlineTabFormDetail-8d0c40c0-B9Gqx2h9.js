import{cl as G,u as X,ah as Y,e as Z,b8 as ee,cr as te,bl as re}from"./index-BnxvmHFv.js";import{f as s,w as oe,r as ae,ag as u,aq as g,ar as m,ah as N,k as v,aG as ie,aD as D,aB as w,F as ne,aC as le,aA as pe}from"./vue-vendor-CnsIXr4W.js";import{bA as se}from"./antd-vue-vendor-ac69AFxs.js";import me from"./DetailForm-deecbf3d-D32RlJyb.js";import ue from"./OnlineSubFormDetail-58e896ce-C6rEQhA2.js";import{k as ce}from"./useExtendComponent-dcae5c78-C--8iMn7.js";import{t as de,s as fe}from"./constant-fa63bd66-Ddbq-fz2.js";import"./index-_aSFLs8i.js";import"./componentMap-Bg13cs1o.js";import"./index-j6u-UpGU.js";import"./index-D4YmpXsT.js";import"./index-C9Hr1YQz.js";import"./user.api-_jc-Tnyz.js";import"./customExpression-CtXxD800.js";import"./index-DjTWLsmR.js";import"./useListPage-BCVdKj1d.js";import"./LinkTableListPiece-f3a8e0d7-BR0-U9EN.js";import"./OnlineSelectCascade-05c40fef-PZGR6IPg.js";import"./JModalTip-b055ab60-Co9Wp31o.js";import"./vxe-table-vendor-CK0mysZD.js";import"./JUpload-DCci6z5o.js";import"./useForm-DNLebgae.js";import"./index-BNka1IZr.js";import"./index-DmtYkh1u.js";import"./index-B4xK7O2u.js";import"./useTimeout-DoPujGLn.js";import"./useIntersectionObserver-DZYoTRf_.js";import"./BasicModal-B-mPgnfO.js";import"./useWindowSizeFn-BcaCTZPc.js";import"./BasicTable-DO8Vk_Gm.js";import"./injectionKey-DPVn4AgL.js";import"./JAreaLinkage-BDz8pfno.js";import"./areaDataUtil-ChdaTOUz.js";import"./JSelectUser-Blw_A8N4.js";import"./props-DHLM7ils.js";import"./JSelectBiz-rbVGd4Dv.js";import"./JSelectDept-1TdcISOX.js";import"./index-Dr3sBsWR.js";import"./bem-DfjdT9o5.js";import"./props-WP5sspMu.js";import"./useContextMenu-DwGGVBbH.js";import"./JCodeEditor-C8R0_wo6.js";import"./useFormItem-Eoh0MXq5.js";import"./htmlmixed-XbbCJteX.js";import"./vue-BLlfMlNv.js";/* empty css             */import"./JAddInput-B7aUDeJi.js";import"./index-86WEzeSw.js";import"./depart.api-BTFSMQ51.js";import"./JPopup-gJ1mXmQZ.js";import"./EasyCronInput-BB8IcbzA.js";import"./JEllipsis-IqMnhApY.js";import"./download-ZmOT1Dx2.js";import"./base64Conver-24EVOS6V.js";import"./index-DBQnQ9LG.js";import"./index-CUrp7h3O.js";import"./useCountdown-D_p0DKZc.js";import"./useFormItemSingle-D8EqzImO.js";import"./index-C3YRp4LI.js";import"./index-mK2t00gc.js";var be=Object.defineProperty,B=Object.getOwnPropertySymbols,he=Object.prototype.hasOwnProperty,ye=Object.prototype.propertyIsEnumerable,I=(n,a,o)=>a in n?be(n,a,{enumerable:!0,configurable:!0,writable:!0,value:o}):n[a]=o,ge=(n,a)=>{for(var o in a||(a={}))he.call(a,o)&&I(n,o,a[o]);if(B)for(var o of B(a))ye.call(a,o)&&I(n,o,a[o]);return n},A=(n,a,o)=>new Promise((r,b)=>{var S=l=>{try{c(o.next(l))}catch(d){b(d)}},y=l=>{try{c(o.throw(l))}catch(d){b(d)}},c=l=>l.done?r(l.value):Promise.resolve(l.value).then(S,y);c((o=o.apply(n,a)).next())});const ve={name:"OnlineTabFormDetail",components:{DetailForm:me,Loading:re,PrinterOutlined:se,OnlineSubFormDetail:ue},props:{id:{type:String,default:""},formTemplate:{type:Number,default:1},disabled:{type:Boolean,default:!1},isTree:{type:Boolean,default:!1},pidField:{type:String,default:""},submitTip:{type:Boolean,default:!0},showSub:{type:Boolean,default:!0},themeTemplate:{type:String,default:""},tabIndex:{type:String,default:""}},emits:["success","rendered"],setup(n,{emit:a}){const{createMessage:o}=X(),{getIsMobile:r}=Y(),b=s(""),S=s(!0),y=s(!1),c=s(1),l=s({}),d=s("auto"),k=s(340),T=s("0"),P=s(!r.value);oe(()=>n.tabIndex,(e,t)=>{T.value=e,t&&K()},{immediate:!0});const h=ae({reportPrintShow:0,reportPrintUrl:"",joinQuery:0,modelFullscreen:0,modalMinWidth:""}),{detailFormSchemas:p,hasSubTable:x,subTabInfo:M,refMap:R,showStatus:F,subDataSource:C,createFormSchemas:_,formSpan:J}=ce(n);function U(e){let t={reportPrintShow:0,reportPrintUrl:"",joinQuery:0,modelFullscreen:1,modalMinWidth:""};e&&(t=JSON.parse(e)),Object.keys(t).map(i=>{h[i]=t[i]})}function V(e){return A(this,null,function*(){c.value=e.head.tableType,b.value=e.head.tableName,S.value=e.head.tableType==1,U(e.head.extConfigJson),_(e.schema.properties),a("rendered",h)})}function E(e,t){return A(this,null,function*(){yield L(t),O(!0)})}function H(e){let t=`/online/cgform/api/detail/${n.id}/${e}`;return new Promise((i,f)=>{Z.get({url:t},{isTransformResponse:!1}).then(j=>{j.success?i(j.result):(f(),o.warning(j.message))}).catch(()=>{f()})})}function O(e){Object.keys(F).map(t=>{F[t]=e})}function K(){O(!1),setTimeout(()=>{O(!0)},300)}function L(e){return A(this,null,function*(){let t=yield H(e.id);l.value=ge({},t),z(t)})}function z(e){e||(e={});let t=Object.keys(C.value);if(t&&t.length>0){let i={};for(let f of t)i[f]=e[f]||[];C.value=i}}function $(e){return"online_"+e+":"}function Q(){let e=h.reportPrintUrl,t=l.value;if(t){let i=t.id,f=ee();te(e,i,f)}}function W(e){let t=l.value;return q(t,e)}function q(e,t){if(e){let i=e[t];return!i&&i!==0&&(i=e[t.toLowerCase()],!i&&i!==0&&(i=e[t.toUpperCase()])),i}return""}return{detailFormSchemas:p,formData:l,formSpan:J,tableName:b,loading:y,hasSubTable:x,subTabInfo:M,subFormHeight:d,subTableHeight:k,refMap:R,onTabChange:K,subDataSource:C,getSubTableAuthPre:$,show:E,createRootProperties:V,onOpenReportPrint:Q,onlineExtConfigJson:h,getSubTableForeignKeyValue:W,showStatus:F,ERP:fe,TAB:de,subActiveKey:T,rowNumber:P}}},Se=["id"],Te={key:0,style:{"text-align":"right",position:"absolute",top:"15px",right:"20px","z-index":"999"}},we={key:1};function ke(n,a,o,r,b,S){const y=u("PrinterOutlined"),c=u("detail-form"),l=u("a-tab-pane"),d=u("online-sub-form-detail"),k=u("JVxeTable"),T=u("a-spin"),P=u("a-tabs"),h=u("Loading");return m(),g("div",{id:r.tableName+"_form"},[r.formData.id&&r.onlineExtConfigJson.reportPrintShow?(m(),g("div",Te,[v(y,{title:"打印",onClick:r.onOpenReportPrint,style:{"font-size":"16px"}},null,8,["onClick"])])):N("",!0),v(P,{class:"tabTheme",onChange:r.onTabChange,activeKey:r.subActiveKey,"onUpdate:activeKey":a[0]||(a[0]=p=>r.subActiveKey=p)},{default:D(()=>[(m(),w(l,{tab:"主表",key:"-1"},{default:D(()=>[v(c,{schemas:r.detailFormSchemas,data:r.formData,span:r.formSpan},null,8,["schemas","data","span"])]),_:1})),r.hasSubTable&&o.showSub?(m(!0),g(ne,{key:0},le(r.subTabInfo,(p,x)=>(m(),w(l,{tab:p.describe,key:x+"",forceRender:!0},{default:D(()=>[p.relationType==1?(m(),g("div",{key:0,style:pe({"overflow-y":"auto","overflow-x":"hidden","max-height":r.subFormHeight+"px"})},[v(d,{table:p.key,"form-template":o.formTemplate,"main-id":r.getSubTableForeignKeyValue(p.foreignKey),properties:p.properties},null,8,["table","form-template","main-id","properties"])],4)):(m(),g("div",we,[r.showStatus[p.key]?(m(),w(k,{key:0,ref_for:!0,ref:r.refMap[p.key],toolbar:"","keep-source":"","row-number":r.rowNumber,"row-selection":"",height:r.subTableHeight,disabled:!0,columns:p.columns,dataSource:r.subDataSource[p.key],authPre:r.getSubTableAuthPre(p.key)},null,8,["row-number","height","columns","dataSource","authPre"])):(m(),w(T,{key:1,spinning:!0}))]))]),_:2},1032,["tab"]))),128)):N("",!0)]),_:1},8,["onChange","activeKey"]),v(h,{loading:r.loading,absolute:!1},null,8,["loading"]),ie(n.$slots,"bottom",{},void 0,!0)],8,Se)}const Dt=G(ve,[["render",ke],["__scopeId","data-v-60a1e2da"]]);export{Dt as default};
