{"java.compile.nullAnalysis.mode": "automatic", "svn.delete.ignoredRulesForDeletedFiles": ["jeecg-module-system\\jeecg-system-api\\jeecg-system-cloud-api\\target\\classes\\META-INF", "jeecg-module-system\\jeecg-system-api\\jeecg-system-local-api\\target\\classes\\META-INF", "jeecg-module-system\\jeecg-system-biz\\target\\classes\\META-INF", "jeecg-module-system\\jeecg-system-biz\\target\\classes\\META-INF\\maven\\org.jeecgframework.boot\\jeecg-system-biz\\pom.xml", "jeecg-server-cloud\\jeecg-cloud-gateway\\target\\classes\\META-INF", "jeecg-server-cloud\\jeecg-cloud-gateway\\target\\classes\\META-INF\\maven\\org.jeecgframework.boot\\jeecg-cloud-gateway\\pom.properties", "jeecg-server-cloud\\jeecg-cloud-nacos\\target\\classes\\META-INF", "jeecg-server-cloud\\jeecg-demo-cloud-start\\target\\classes\\META-INF", "jeecg-server-cloud\\jeecg-system-cloud-start\\target\\classes\\META-INF", "jeecg-server-cloud\\jeecg-visual\\jeecg-cloud-monitor\\target\\classes\\META-INF", "jeecg-server-cloud\\jeecg-visual\\jeecg-cloud-sentinel\\target\\classes\\META-INF", "jeecg-server-cloud\\jeecg-visual\\jeecg-cloud-test\\jeecg-cloud-test-more\\target\\classes\\META-INF", "jeecg-server-cloud\\jeecg-visual\\jeecg-cloud-test\\jeecg-cloud-test-rabbitmq\\target\\classes\\META-INF", "jeecg-server-cloud\\jeecg-visual\\jeecg-cloud-test\\jeecg-cloud-test-rocketmq\\target\\classes\\META-INF", "jeecg-server-cloud\\jeecg-visual\\jeecg-cloud-test\\jeecg-cloud-test-seata\\jeecg-cloud-test-seata-account\\target\\classes\\META-INF", "jeecg-server-cloud\\jeecg-visual\\jeecg-cloud-test\\jeecg-cloud-test-seata\\jeecg-cloud-test-seata-order\\target\\classes\\META-INF", "jeecg-server-cloud\\jeecg-visual\\jeecg-cloud-test\\jeecg-cloud-test-seata\\jeecg-cloud-test-seata-product\\target\\classes\\META-INF", "jeecg-server-cloud\\jeecg-visual\\jeecg-cloud-test\\jeecg-cloud-test-shardingsphere\\target\\classes\\META-INF", "jeecg-server-cloud\\jeecg-visual\\jeecg-cloud-xxljob\\target\\classes\\META-INF", "jeecg-module-system\\jeecg-system-biz\\target\\classes\\jeecg", "jeecg-module-system\\jeecg-system-biz\\target\\classes\\jeecg\\code-template-online\\common\\blob.ftl", "jeecg-module-system\\jeecg-system-biz\\target\\classes\\jeecg\\code-template-online\\common\\form", "jeecg-module-system\\jeecg-system-biz\\target\\classes\\jeecg\\code-template-online\\common\\form\\native", "jeecg-module-system\\jeecg-system-biz\\target\\classes\\jeecg\\code-template-online\\common\\init", "jeecg-module-system\\jeecg-system-biz\\target\\classes\\jeecg\\code-template-online\\common\\sql", "jeecg-module-system\\jeecg-system-biz\\target\\classes\\jeecg\\code-template-online\\common\\utils.ftl", "jeecg-module-system\\jeecg-system-biz\\target\\classes\\jeecg\\code-template-online\\common\\validatorRulesTemplate", "jeecg-module-system\\jeecg-system-biz\\target\\classes\\jeecg\\code-template\\one2\\java\\${bussiPackage}\\controller", "jeecg-module-system\\jeecg-system-biz\\target\\classes\\jeecg\\code-template\\one2\\java\\${bussiPackage}\\entity\\${entityPackage}", "jeecg-module-system\\jeecg-system-biz\\target\\classes\\jeecg\\code-template\\one\\java\\${bussiPackage}\\${entityPackage}\\controller", "jeecg-module-system\\jeecg-system-biz\\target\\classes\\jeecg\\code-template\\one\\java\\${bussiPackage}\\${entityPackage}\\entity", "jeecg-module-system\\jeecg-system-biz\\target\\classes\\jeecg\\code-template\\onetomany2\\java\\${bussiPackage}\\${entityPackage}\\controller", "jeecg-module-system\\jeecg-system-biz\\target\\classes\\jeecg\\code-template\\onetomany2\\java\\${bussiPackage}\\${entityPackage}\\entity\\${entityName}.javai", "jeecg-module-system\\jeecg-system-biz\\target\\classes\\jeecg\\code-template\\onetomany2\\java\\${bussiPackage}\\${entityPackage}\\entity\\[1-n]Entity.javai", "jeecg-module-system\\jeecg-system-biz\\target\\classes\\jeecg\\code-template\\onetomany\\java\\${bussiPackage}\\${entityPackage}\\controller", "jeecg-module-system\\jeecg-system-biz\\target\\classes\\jeecg\\code-template\\onetomany\\java\\${bussiPackage}\\${entityPackage}\\entity\\${entityName}.javai", "jeecg-module-system\\jeecg-system-biz\\target\\classes\\jeecg\\code-template\\onetomany\\java\\${bussiPackage}\\${entityPackage}\\entity\\[1-n]Entity.javai", "jeecg-module-system\\jeecg-system-biz\\target\\classes\\jeecg\\code-template\\onetomany\\java\\${bussiPackage}\\${entityPackage}\\mapper\\${entityName}Mapper.javai", "jeecg-module-system\\jeecg-system-biz\\target\\classes\\jeecg\\code-template\\onetomany\\java\\${bussiPackage}\\${entityPackage}\\mapper\\xml", "jeecg-module-system\\jeecg-system-biz\\target\\classes\\jeecg\\code-template\\onetomany\\java\\${bussiPackage}\\${entityPackage}\\service\\I${entityName}Service.javai", "jeecg-module-system\\jeecg-system-biz\\target\\classes\\jeecg\\code-template\\onetomany\\java\\${bussiPackage}\\${entityPackage}\\service\\impl", "jeecg-module-system\\jeecg-system-biz\\target\\classes\\org\\jeecg\\modules\\jsjx\\mapper\\xml\\JsjxCommonServiceMapper.xml", "jeecg-module-system\\jeecg-system-biz\\target\\classes\\org\\jeecg\\modules\\jsjx\\sysuserlog\\mapper\\xml\\SysUserLogMapper.xml", "jeecg-module-system\\jeecg-system-biz\\target\\classes\\org\\jeecg\\modules\\message\\mapper\\xml\\SysMessageMapper.xml", "jeecg-module-system\\jeecg-system-biz\\target\\classes\\org\\jeecg\\modules\\message\\mapper\\xml\\SysMessageTemplateMapper.xml", "jeecg-module-system\\jeecg-system-biz\\target\\classes\\org\\jeecg\\modules\\quartz\\mapper\\xml\\QuartzJobMapper.xml", "jeecg-module-system\\jeecg-system-biz\\target\\classes\\org\\jeecg\\modules\\system\\mapper\\xml\\SysAnnouncementMapper.xml", "jeecg-module-system\\jeecg-system-biz\\target\\classes\\org\\jeecg\\modules\\system\\mapper\\xml\\SysAnnouncementSendMapper.xml", "jeecg-module-system\\jeecg-system-biz\\target\\classes\\org\\jeecg\\modules\\system\\mapper\\xml\\SysCategoryMapper.xml", "jeecg-module-system\\jeecg-system-biz\\target\\classes\\org\\jeecg\\modules\\system\\mapper\\xml\\SysCheckRuleMapper.xml", "jeecg-module-system\\jeecg-system-biz\\target\\classes\\org\\jeecg\\modules\\system\\mapper\\xml\\SysCommentMapper.xml", "jeecg-module-system\\jeecg-system-biz\\target\\classes\\org\\jeecg\\modules\\system\\mapper\\xml\\SysDataLogMapper.xml", "jeecg-module-system\\jeecg-system-biz\\target\\classes\\org\\jeecg\\modules\\system\\mapper\\xml\\SysDataSourceMapper.xml", "jeecg-module-system\\jeecg-system-biz\\target\\classes\\org\\jeecg\\modules\\system\\mapper\\xml\\SysDepartMapper.xml", "jeecg-module-system\\jeecg-system-biz\\target\\classes\\org\\jeecg\\modules\\system\\mapper\\xml\\SysDepartPermissionMapper.xml", "jeecg-module-system\\jeecg-system-biz\\target\\classes\\org\\jeecg\\modules\\system\\mapper\\xml\\SysDepartRoleMapper.xml", "jeecg-module-system\\jeecg-system-biz\\target\\classes\\org\\jeecg\\modules\\system\\mapper\\xml\\SysDepartRolePermissionMapper.xml", "jeecg-module-system\\jeecg-system-biz\\target\\classes\\org\\jeecg\\modules\\system\\mapper\\xml\\SysDepartRoleUserMapper.xml", "jeecg-module-system\\jeecg-system-biz\\target\\classes\\org\\jeecg\\modules\\system\\mapper\\xml\\SysDictItemMapper.xml", "jeecg-module-system\\jeecg-system-biz\\target\\classes\\org\\jeecg\\modules\\system\\mapper\\xml\\SysDictMapper.xml", "jeecg-module-system\\jeecg-system-biz\\target\\classes\\org\\jeecg\\modules\\system\\mapper\\xml\\SysFillRuleMapper.xml", "jeecg-module-system\\jeecg-system-biz\\target\\classes\\org\\jeecg\\modules\\system\\mapper\\xml\\SysGatewayRouteMapper.xml", "jeecg-module-system\\jeecg-system-biz\\target\\classes\\org\\jeecg\\modules\\system\\mapper\\xml\\SysLogMapper.xml", "jeecg-module-system\\jeecg-system-biz\\target\\classes\\org\\jeecg\\modules\\system\\mapper\\xml\\SysPackPermissionMapper.xml", "jeecg-module-system\\jeecg-system-biz\\target\\classes\\org\\jeecg\\modules\\system\\mapper\\xml\\SysPermissionDataRuleMapper.xml", "jeecg-module-system\\jeecg-system-biz\\target\\classes\\org\\jeecg\\modules\\system\\mapper\\xml\\SysPermissionMapper.xml", "jeecg-module-system\\jeecg-system-biz\\target\\classes\\org\\jeecg\\modules\\system\\mapper\\xml\\SysPositionMapper.xml", "jeecg-module-system\\jeecg-system-biz\\target\\classes\\org\\jeecg\\modules\\system\\mapper\\xml\\SysRoleIndexMapper.xml", "jeecg-module-system\\jeecg-system-biz\\target\\classes\\org\\jeecg\\modules\\system\\mapper\\xml\\SysRoleMapper.xml", "jeecg-module-system\\jeecg-system-biz\\target\\classes\\org\\jeecg\\modules\\system\\mapper\\xml\\SysTableWhiteListMapper.xml", "jeecg-module-system\\jeecg-system-biz\\target\\classes\\org\\jeecg\\modules\\system\\mapper\\xml\\SysTenantMapper.xml", "jeecg-module-system\\jeecg-system-biz\\target\\classes\\org\\jeecg\\modules\\system\\mapper\\xml\\SysTenantPackMapper.xml", "jeecg-module-system\\jeecg-system-biz\\target\\classes\\org\\jeecg\\modules\\system\\mapper\\xml\\SysTenantPackUserMapper.xml", "jeecg-module-system\\jeecg-system-biz\\target\\classes\\org\\jeecg\\modules\\system\\mapper\\xml\\SysThirdAccountMapper.xml", "jeecg-module-system\\jeecg-system-biz\\target\\classes\\org\\jeecg\\modules\\system\\mapper\\xml\\SysThirdAppConfigMapper.xml", "jeecg-module-system\\jeecg-system-biz\\target\\classes\\org\\jeecg\\modules\\system\\mapper\\xml\\SysUserAgentMapper.xml", "jeecg-module-system\\jeecg-system-biz\\target\\classes\\org\\jeecg\\modules\\system\\mapper\\xml\\SysUserDepartMapper.xml", "jeecg-module-system\\jeecg-system-biz\\target\\classes\\org\\jeecg\\modules\\system\\mapper\\xml\\SysUserMapper.xml", "jeecg-module-system\\jeecg-system-biz\\target\\classes\\org\\jeecg\\modules\\system\\mapper\\xml\\SysUserPositionMapper.xml", "jeecg-module-system\\jeecg-system-biz\\target\\classes\\org\\jeecg\\modules\\system\\mapper\\xml\\SysUserTenantMapper.xml", "jeecg-module-system\\jeecg-system-biz\\target\\classes\\static", "jeecg-module-system\\jeecg-system-biz\\target\\classes\\templates", "jeecg-server-cloud\\jeecg-visual\\jeecg-cloud-sentinel\\target\\classes\\META-INF\\maven\\org.jeecgframework.boot\\jeecg-cloud-sentinel\\pom.properties", "jeecg-server-cloud\\jeecg-visual\\jeecg-cloud-test\\jeecg-cloud-test-rabbitmq\\target\\classes\\META-INF\\maven\\org.jeecgframework.boot\\jeecg-cloud-test-rabbitmq\\pom.properties", "jeecg-server-cloud\\jeecg-visual\\jeecg-cloud-test\\jeecg-cloud-test-rocketmq\\target\\classes\\META-INF\\maven\\org.jeecgframework.boot\\jeecg-cloud-test-rocketmq\\pom.properties", "jeecg-server-cloud\\jeecg-visual\\jeecg-cloud-test\\jeecg-cloud-test-seata\\jeecg-cloud-test-seata-account\\target\\classes\\META-INF\\maven\\org.jeecgframework.boot\\jeecg-cloud-test-seata-account\\pom.properties", "jeecg-module-system\\jeecg-system-biz\\target\\test-classes"]}