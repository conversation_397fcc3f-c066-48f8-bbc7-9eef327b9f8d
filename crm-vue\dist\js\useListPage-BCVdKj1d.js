var A=Object.defineProperty,E=Object.defineProperties;var I=Object.getOwnPropertyDescriptors;var F=Object.getOwnPropertySymbols;var M=Object.prototype.hasOwnProperty,X=Object.prototype.propertyIsEnumerable;var O=(e,o,t)=>o in e?A(e,o,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[o]=t,C=(e,o)=>{for(var t in o||(o={}))M.call(o,t)&&O(e,t,o[t]);if(F)for(var t of F(o))X.call(o,t)&&O(e,t,o[t]);return e},S=(e,o)=>E(e,I(o));var v=(e,o,t)=>new Promise((g,f)=>{var s=n=>{try{m(t.next(n))}catch(d){f(d)}},a=n=>{try{m(t.throw(n))}catch(d){f(d)}},m=n=>n.done?g(n.value):Promise.resolve(n.value).then(s,a);m((t=t.apply(e,o)).next())});import{u as k,f as j,r as D}from"./vue-vendor-CnsIXr4W.js";import{w as K}from"./antd-vue-vendor-ac69AFxs.js";import{u as L}from"./index-DjTWLsmR.js";import{C as P,u as U,D as W,l as $,E as q}from"./index-BnxvmHFv.js";import"./BasicTable-DO8Vk_Gm.js";import"./index-D4YmpXsT.js";import"./useForm-DNLebgae.js";import"./componentMap-Bg13cs1o.js";import"./useFormItem-Eoh0MXq5.js";import"./index-j6u-UpGU.js";import"./BasicModal-B-mPgnfO.js";import"./useTimeout-DoPujGLn.js";import"./vxe-table-vendor-CK0mysZD.js";import"./useWindowSizeFn-BcaCTZPc.js";import"./index-BNka1IZr.js";import"./index-DmtYkh1u.js";import"./index-B4xK7O2u.js";import"./useIntersectionObserver-DZYoTRf_.js";import"./download-ZmOT1Dx2.js";import"./base64Conver-24EVOS6V.js";import"./index-DBQnQ9LG.js";import"./index-CUrp7h3O.js";import"./useCountdown-D_p0DKZc.js";import"./useFormItemSingle-D8EqzImO.js";import"./JSelectUser-Blw_A8N4.js";import"./props-DHLM7ils.js";import"./JSelectBiz-rbVGd4Dv.js";import"./JAddInput-B7aUDeJi.js";import"./areaDataUtil-ChdaTOUz.js";import"./index-86WEzeSw.js";import"./index-Dr3sBsWR.js";import"./bem-DfjdT9o5.js";import"./props-WP5sspMu.js";import"./useContextMenu-DwGGVBbH.js";import"./depart.api-BTFSMQ51.js";import"./JSelectDept-1TdcISOX.js";import"./JPopup-gJ1mXmQZ.js";import"./JEllipsis-IqMnhApY.js";import"./JUpload-DCci6z5o.js";import"./index-C3YRp4LI.js";import"./index-mK2t00gc.js";import"./JAreaLinkage-BDz8pfno.js";import"./JCodeEditor-C8R0_wo6.js";import"./htmlmixed-XbbCJteX.js";import"./vue-BLlfMlNv.js";/* empty css             */import"./EasyCronInput-BB8IcbzA.js";import"./injectionKey-DPVn4AgL.js";const{handleExportXls:z,handleImportXls:B}=P();function $e(e){const o=U();let t={};e.designScope&&(t=W(e.designScope));const g=H(e.tableProps),[,{getForm:f,reload:s,setLoading:a},{selectedRowKeys:m}]=g;function n(){return v(this,null,function*(){var p,x;let{url:r,name:i,params:l}=(p=e==null?void 0:e.exportConfig)!=null?p:{},u=typeof r=="function"?r():r;if(u){let b=typeof i=="function"?i():i,c={};try{((x=e==null?void 0:e.tableProps)==null?void 0:x.useSearchForm)!==!1&&(c=yield f().validate())}catch(w){}if(c!=null&&c.column||Object.assign(c,{column:"createTime",order:"desc"}),l){const w=$(l)?yield l():C({},l||{});Object.keys(w).map(R=>{let T=w[R];T&&(c[R]=k(T))})}return m.value&&m.value.length>0&&(c.selections=m.value.join(",")),z(b,u,q(c))}else return o.createMessage.warn("没有传递 exportConfig.url 参数"),Promise.reject()})}function d(r){var p;let{url:i,success:l}=(p=e==null?void 0:e.importConfig)!=null?p:{},u=typeof i=="function"?i():i;return u?B(r,u,l||s):(o.createMessage.warn("没有传递 importConfig.url 参数"),Promise.reject())}function h(r,i){return new Promise((l,u)=>{var x;const p=()=>v(this,null,function*(){var b,c;try{a(!0);const w=yield r();((b=i==null?void 0:i.reload)==null||b)&&s(),((c=i==null?void 0:i.clearSelection)==null||c)&&(m.value=[]),l(w)}catch(w){u(w)}finally{a(!1)}});(x=i==null?void 0:i.confirm)==null||x?o.createConfirm({iconType:"warning",title:"删除",content:"确定要删除吗？",onOk:()=>p(),onCancel:()=>u()}):p()})}function y(r){return h(r,{confirm:!1,clearSelection:!1})}return S(C(C({},t),o),{onExportXls:n,onImportXls:d,doRequest:h,doDeleteRecord:y,tableContext:g})}function H(e){var d,h,y;const o={xs:24,sm:12,md:12,lg:8,xl:8,xxl:6},t={rowKey:"id",useSearchForm:!0,formConfig:{compact:!0,autoSubmitOnEnter:!0,rowProps:{gutter:8},baseColProps:C({},o),labelCol:{xs:24,sm:8,md:6,lg:8,xl:6,xxl:6},wrapperCol:{},showAdvancedButton:!0,autoAdvancedCol:3,actionColOptions:S(C({},o),{style:{textAlign:"left"}})},striped:!1,canResize:!0,minHeight:300,clickToRowSelect:!1,bordered:!0,showIndexColumn:!1,showTableSetting:!0,tableSetting:{fullScreen:!1},showActionColumn:!0,actionColumn:{width:120,title:"操作",fixed:!1,dataIndex:"action",slots:{customRender:"action"}}};e&&(e.formConfig&&n(e.formConfig),K(t,e));function g(r){return Object.assign({column:"createTime",order:"desc"},r)}Object.assign(t,{beforeFetch:g}),typeof e.beforeFetch=="function"&&(t.beforeFetch=function(r){return r=g(r),e.beforeFetch(r)});const f=j([]),s=j([]),a=(d=e==null?void 0:e.rowSelection)!=null?d:{},m=D(S(C({},a),{type:(h=a.type)!=null?h:"checkbox",columnWidth:(y=a.columnWidth)!=null?y:50,selectedRows:s,selectedRowKeys:f,onChange(...r){f.value=r[0],s.value=r[1],typeof a.onChange=="function"&&a.onChange(...r)}}));delete t.rowSelection;function n(r){const i=["baseColProps","labelCol"];for(let l of i)if(r&&r[l]){if(t.formConfig){let u=t.formConfig;u[l]=r[l]}r[l]={}}}return[...L(t),{selectedRows:s,selectedRowKeys:f,rowSelection:m}]}export{$e as useListPage,H as useListTable};
