package org.jeecg.modules.crmfy.crmcommissioncalculation.vo;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;

/**
 * @Description: 佣金计算查询结果VO
 * @Author: jeecg-boot
 * @Date: 2025-04-11
 * @Version: V1.0
 */
@Data
@ApiModel(value="CrmCommissionCalculationQueryVO对象", description="佣金计算查询结果")
public class CrmCommissionCalculationQueryVO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**保单号*/
    @Excel(name = "保单号", width = 15)
    @ApiModelProperty(value = "保单号")
    private String policyNo;
    
    /**保单生效日期(计算日期)*/
    @Excel(name = "保单生效日期", width = 15, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "保单生效日期")
    private Date calculationDate;
    
    /**产品英文名称*/
    @Excel(name = "产品英文名称", width = 20)
    @ApiModelProperty(value = "产品英文名称")
    private String planName;
    
    /**佣金期数*/
    @Excel(name = "佣金期数", width = 10)
    @ApiModelProperty(value = "佣金期数")
    private Integer yearNum;
    
    /**保费金额*/
    @Excel(name = "保费金额", width = 15)
    @ApiModelProperty(value = "保费金额")
    private BigDecimal premiumAmount;
    
    /**佣金金额*/
    @Excel(name = "佣金金额", width = 15)
    @ApiModelProperty(value = "佣金金额")
    private BigDecimal commissionAmount;
    
    /**缴费币种*/
    @Excel(name = "缴费币种", width = 10)
    @ApiModelProperty(value = "缴费币种")
    private String paymentCurrency;
    
    /**合约手续费率*/
    @Excel(name = "合约手续费率", width = 15)
    @ApiModelProperty(value = "合约手续费率")
    private BigDecimal contractRate;
    
    /**销售员佣金率*/
    @Excel(name = "销售员佣金率", width = 15)
    @ApiModelProperty(value = "销售员佣金率")
    private BigDecimal commissionRate;
    
    /**佣金结算日当天汇率*/
    @Excel(name = "佣金结算日当天汇率", width = 20)
    @ApiModelProperty(value = "佣金结算日当天汇率")
    private BigDecimal rcExchangeRate;
}
