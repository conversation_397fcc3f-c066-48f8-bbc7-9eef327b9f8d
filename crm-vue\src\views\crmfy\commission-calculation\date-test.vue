<template>
  <div class="p-4">
    <h2>日期转换测试页面</h2>
    
    <a-card title="表单测试" class="mb-4">
      <BasicForm @register="registerForm" @submit="handleSubmit" />
    </a-card>

    <a-card title="提交结果" v-if="submitResult">
      <pre>{{ JSON.stringify(submitResult, null, 2) }}</pre>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import { BasicForm, useForm, FormSchema } from '/@/components/Form';
  import { message } from 'ant-design-vue';

  const submitResult = ref(null);

  const schemas: FormSchema[] = [
    {
      field: 'policyNo',
      label: '保单号',
      component: 'Input',
      colProps: { span: 6 },
    },
    {
      field: 'companyCode',
      label: '公司',
      component: 'JDictSelectTag',
      componentProps: {
        dictCode: 'supplier',
        placeholder: '请选择公司',
      },
      colProps: { span: 6 },
    },
    {
      field: 'yearNum',
      label: '佣金期数',
      component: 'InputNumber',
      componentProps: {
        placeholder: '请输入佣金期数',
        min: 1,
      },
      colProps: { span: 6 },
    },
    {
      field: '[calculationDateStart, calculationDateEnd]',
      label: '保单生效日期',
      component: 'RangePicker',
      componentProps: {
        format: 'YYYY-MM-DD',
        placeholder: ['开始日期', '结束日期'],
      },
      colProps: { span: 6 },
    },
  ];

  const [registerForm] = useForm({
    schemas,
    showActionButtonGroup: true,
    actionColOptions: {
      span: 24,
    },
    fieldMapToTime: [
      ['[calculationDateStart, calculationDateEnd]', ['calculationDateStart', 'calculationDateEnd'], 'YYYY-MM-DD']
    ],
  });

  function handleSubmit(values: any) {
    console.log('表单提交数据:', values);
    submitResult.value = values;
    message.success('表单提交成功，请查看结果');
  }
</script>

<style scoped>
  .mb-4 {
    margin-bottom: 16px;
  }
</style>
