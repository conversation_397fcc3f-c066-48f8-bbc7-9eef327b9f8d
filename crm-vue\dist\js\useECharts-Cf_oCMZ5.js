var x=Object.defineProperty;var k=Object.getOwnPropertySymbols;var C=Object.prototype.hasOwnProperty,b=Object.prototype.propertyIsEnumerable;var w=(r,t,n)=>t in r?x(r,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):r[t]=n,z=(r,t)=>{for(var n in t||(t={}))C.call(t,n)&&w(r,n,t[n]);if(k)for(var n of k(t))b.call(t,n)&&w(r,n,t[n]);return r};import{useTimeoutFn as d}from"./useTimeout-DoPujGLn.js";import{B as y,s as B,x as H,al as R,m as S}from"./index-BnxvmHFv.js";import{e as D,f as T,u as i,n as h,w as L}from"./vue-vendor-CnsIXr4W.js";import{e as O}from"./echarts-CVChFi7F.js";import"./antd-vue-vendor-ac69AFxs.js";import"./vxe-table-vendor-CK0mysZD.js";function N(r,t="default"){const{getDarkMode:n}=y(),u=D(()=>t==="default"?n.value:t);let e=null,l=c;const a=T({});let v=()=>{};l=B(c,200);const g=D(()=>u.value!=="dark"?a.value:z({backgroundColor:"transparent"},a.value));function m(s=t){const o=i(r);if(!o||!i(o))return;e=O.init(o,s);const{removeEvent:f}=H({el:window,name:"resize",listener:l});v=f;const{widthRef:F,screenEnum:M}=R();(i(F)<=M.MD||o.offsetHeight===0)&&d(()=>{l()},30)}function p(s,o=!0){var f;if(a.value=s,((f=i(r))==null?void 0:f.offsetHeight)===0){d(()=>{p(i(g))},30);return}h(()=>{d(()=>{!e&&(m(u.value),!e)||(o&&(e==null||e.clear()),e==null||e.setOption(i(g)))},30)})}function c(){e==null||e.resize()}L(()=>u.value,s=>{e&&(e.dispose(),m(s),p(a.value))}),S(()=>{e&&(v(),e.dispose(),e=null)});function E(){return e||m(u.value),e}return{setOptions:p,resize:c,echarts:O,getInstance:E}}export{N as useECharts};
