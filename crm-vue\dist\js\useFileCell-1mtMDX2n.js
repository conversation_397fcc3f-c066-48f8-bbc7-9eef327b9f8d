var V=Object.defineProperty,J=Object.defineProperties;var w=Object.getOwnPropertyDescriptors;var c=Object.getOwnPropertySymbols;var F=Object.prototype.hasOwnProperty,O=Object.prototype.propertyIsEnumerable;var f=(e,a,l)=>a in e?V(e,a,{enumerable:!0,configurable:!0,writable:!0,value:l}):e[a]=l,r=(e,a)=>{for(var l in a||(a={}))F.call(a,l)&&f(e,l,a[l]);if(c)for(var l of c(a))O.call(a,l)&&f(e,l,a[l]);return e},s=(e,a)=>J(e,w(a));import{e as i}from"./vue-vendor-CnsIXr4W.js";import{ci as U,cj as _,r as b,ck as j,aB as k,aa as D}from"./index-BnxvmHFv.js";import{_ as E}from"./index-C9Hr1YQz.js";import"./index-j6u-UpGU.js";import{ai as N,D as S}from"./antd-vue-vendor-ac69AFxs.js";function z(e,a,l){const m=j(e,r({token:!0,action:k},l)),{innerFile:n,handleChangeCommon:d,originColumn:t}=m,[v,{openModal:h}]=D(),C=i(()=>{let o=5,u=n.value;return!u||!u.name?"":u.name.length>o?u.name.substr(0,o)+"…":u.name}),g=i(()=>{if(n.value){if(n.value.url)return n.value.url;if(n.value.path)return n.value.path}return""}),p=i(()=>{let o=t.value.maxCount;if(t.value&&t.value.fieldExtendJson){let u=JSON.parse(t.value.fieldExtendJson);o=u.uploadnum?u.uploadnum:0}return o!=null?o:0});function x(){h(!0,s(r({removeConfirm:!0,mover:!0,download:!0},t.value.props),{maxCount:p.value,fileType:a}))}function M(o){o?(n.value===null&&(n.value={}),n.value.path=o,d(n.value)):d(null)}return s(r({},m),{modalValue:g,maxCount:p,ellipsisFileName:C,registerModel:v,onModalChange:M,handleMoreOperation:x})}const A={Icon:b,Dropdown:S,LoadingOutlined:N,JUploadModal:E},H={switches:{visible:!0},getValue:e=>_(e),setValue:e=>U(e)};export{A as c,H as e,z as u};
