package org.jeecg.modules.crmfy.crmcommissioncalculation.vo;

import java.io.Serializable;
import java.util.Date;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * @Description: 佣金计算查询参数
 * @Author: jeecg-boot
 * @Date: 2025-04-11
 * @Version: V1.0
 */
@Data
@ApiModel(value="CrmCommissionCalculationQueryPO对象", description="佣金计算查询参数")
public class CrmCommissionCalculationQueryPO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**保单号*/
    @ApiModelProperty(value = "保单号")
    private String policyNo;
    
    /**公司编码*/
    @ApiModelProperty(value = "公司编码")
    private String companyCode;
    
    /**佣金期数*/
    @ApiModelProperty(value = "佣金期数")
    private Integer yearNum;
    
    /**保单生效日期开始*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "保单生效日期开始")
    private String calculationDateStart;
    
    /**保单生效日期结束*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "保单生效日期结束")
    private String calculationDateEnd;
}
