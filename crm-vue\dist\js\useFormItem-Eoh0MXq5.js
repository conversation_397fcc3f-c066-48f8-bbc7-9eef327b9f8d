import{g as v,r as i,av as g,h as I,e as d,n as c,J as h,u as x}from"./vue-vendor-CnsIXr4W.js";import{a5 as C,ak as F}from"./antd-vue-vendor-ac69AFxs.js";function E(s,o="value",l="change",m){const a=v(),n=a==null?void 0:a.emit,r=C.useInjectFormItemContext(),t=i({value:s[o]}),u=g(t),f=e=>{t.value=e};return I(()=>{t.value=s[o]}),[d({get(){return t.value==null||t.value===""?[]:t.value},set(e){F(e,u.value)||(t.value=e,c(()=>{n==null||n(l,e,...h(x(m))||[]),c(()=>r.onFieldChange())}))}}),f,u,r]}export{E as useRuleFormItem};
