// 路由配置示例
// 需要在主路由文件中添加以下配置

export const commissionCalculationRoutes = [
  {
    path: '/crmfy/commission-calculation',
    name: 'CommissionCalculation',
    component: () => import('/@/views/crmfy/commission-calculation/index.vue'),
    meta: {
      title: '保险公司账单列表',
      icon: 'ant-design:calculator-outlined',
      // 权限配置
      permissions: ['crmcommissioncalculation:query'],
    },
  },
];

// 菜单配置示例
export const commissionCalculationMenu = {
  id: 'commission-calculation',
  name: '保险公司账单列表',
  path: '/crmfy/commission-calculation',
  component: 'views/crmfy/commission-calculation/index',
  icon: 'ant-design:calculator-outlined',
  orderNum: 10,
  parentId: 'crmfy', // 父菜单ID
  type: 1, // 菜单类型：1-菜单，2-按钮
  permissions: [
    {
      name: '查询',
      code: 'crmcommissioncalculation:query',
      type: 2,
    },
    {
      name: '导出',
      code: 'crmcommissioncalculation:export',
      type: 2,
    },
    {
      name: '新增',
      code: 'crmcommissioncalculation:add',
      type: 2,
    },
    {
      name: '编辑',
      code: 'crmcommissioncalculation:edit',
      type: 2,
    },
    {
      name: '删除',
      code: 'crmcommissioncalculation:delete',
      type: 2,
    },
  ],
};
