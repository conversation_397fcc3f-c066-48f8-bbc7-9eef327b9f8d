# 佣金计算查询功能部署指南

## 功能概述

本功能实现了分页查询订单佣金信息和导出Excel功能，用于生成保险公司账单列表。

## 后端部署

### 1. 文件清单

**新增文件:**
- `CrmCommissionCalculationQueryPO.java` - 查询参数类
- `CrmCommissionCalculationQueryVO.java` - 查询结果VO类

**修改文件:**
- `CrmCommissionCalculationMapper.java` - 添加查询方法
- `CrmCommissionCalculationMapper.xml` - 添加SQL查询
- `ICrmCommissionCalculationService.java` - 添加服务接口
- `CrmCommissionCalculationServiceImpl.java` - 添加服务实现
- `CrmCommissionCalculationController.java` - 添加新接口

### 2. 数据库要求

确保以下表存在且有数据：
- `crm_commission_calculation` - 佣金计算表
- `crm_policies` - 保单表
- `crm_policy_versions` - 保单版本表

### 3. API接口

**查询接口:**
```
GET /crmcommissioncalculation/crmCommissionCalculation/queryCommissionInfo
```

**导出接口:**
```
GET /crmcommissioncalculation/crmCommissionCalculation/exportCommissionInfoXls
```

## 前端部署

### 1. 文件清单

**新增目录:**
```
crm-vue/src/views/crmfy/commission-calculation/
├── CrmCommissionCalculation.api.ts
├── CrmCommissionCalculation.data.ts
├── CrmCommissionCalculationList.vue
├── index.vue
├── modules/
│   └── CrmCommissionCalculationModal.vue
├── README.md
├── routes.example.ts
└── test.vue
```

### 2. 路由配置

在主路由文件中添加：

```typescript
{
  path: '/crmfy/commission-calculation',
  name: 'CommissionCalculation',
  component: () => import('/@/views/crmfy/commission-calculation/index.vue'),
  meta: {
    title: '保险公司账单列表',
    icon: 'ant-design:calculator-outlined',
  },
}
```

### 3. 菜单配置

在系统管理中添加菜单：
- 菜单名称: 保险公司账单列表
- 菜单路径: /crmfy/commission-calculation
- 组件路径: views/crmfy/commission-calculation/index
- 图标: ant-design:calculator-outlined

## 权限配置

### 1. 后端权限

在数据库 `sys_permission` 表中添加权限记录：

```sql
-- 主菜单权限
INSERT INTO sys_permission (id, parent_id, name, url, component, menu_type, perms, perms_type, sort_no, icon, is_leaf, keep_alive, hidden, description, create_by, create_time, status) 
VALUES ('commission_calculation_menu', 'crmfy_parent_id', '保险公司账单列表', '/crmfy/commission-calculation', 'views/crmfy/commission-calculation/index', 1, NULL, '1', 10, 'ant-design:calculator-outlined', 0, 0, 0, '保险公司账单列表管理', 'admin', NOW(), '1');

-- 查询权限
INSERT INTO sys_permission (id, parent_id, name, menu_type, perms, perms_type, sort_no, is_leaf, description, create_by, create_time, status) 
VALUES ('commission_calculation_query', 'commission_calculation_menu', '查询', 2, 'crmcommissioncalculation:query', '1', 1, 1, '查询佣金计算信息', 'admin', NOW(), '1');

-- 导出权限
INSERT INTO sys_permission (id, parent_id, name, menu_type, perms, perms_type, sort_no, is_leaf, description, create_by, create_time, status) 
VALUES ('commission_calculation_export', 'commission_calculation_menu', '导出', 2, 'crmcommissioncalculation:export', '1', 2, 1, '导出佣金计算信息', 'admin', NOW(), '1');

-- 新增权限
INSERT INTO sys_permission (id, parent_id, name, menu_type, perms, perms_type, sort_no, is_leaf, description, create_by, create_time, status) 
VALUES ('commission_calculation_add', 'commission_calculation_menu', '新增', 2, 'crmcommissioncalculation:add', '1', 3, 1, '新增佣金计算记录', 'admin', NOW(), '1');

-- 编辑权限
INSERT INTO sys_permission (id, parent_id, name, menu_type, perms, perms_type, sort_no, is_leaf, description, create_by, create_time, status) 
VALUES ('commission_calculation_edit', 'commission_calculation_menu', '编辑', 2, 'crmcommissioncalculation:edit', '1', 4, 1, '编辑佣金计算记录', 'admin', NOW(), '1');

-- 删除权限
INSERT INTO sys_permission (id, parent_id, name, menu_type, perms, perms_type, sort_no, is_leaf, description, create_by, create_time, status) 
VALUES ('commission_calculation_delete', 'commission_calculation_menu', '删除', 2, 'crmcommissioncalculation:delete', '1', 5, 1, '删除佣金计算记录', 'admin', NOW(), '1');
```

### 2. 角色权限分配

为相应角色分配权限：

```sql
-- 为管理员角色分配权限
INSERT INTO sys_role_permission (role_id, permission_id) 
SELECT 'admin_role_id', id FROM sys_permission WHERE id LIKE 'commission_calculation_%';
```

## 测试验证

### 1. 后端测试

使用Postman或其他API测试工具：

```bash
# 查询测试
GET http://localhost:8080/jeecg-boot/crmcommissioncalculation/crmCommissionCalculation/queryCommissionInfo?pageNo=1&pageSize=10

# 导出测试
GET http://localhost:8080/jeecg-boot/crmcommissioncalculation/crmCommissionCalculation/exportCommissionInfoXls
```

### 2. 前端测试

1. 访问测试页面: `/crmfy/commission-calculation/test`
2. 输入查询条件进行测试
3. 验证查询结果和导出功能

### 3. 功能验证清单

- [ ] 查询接口正常返回数据
- [ ] 分页功能正常工作
- [ ] 查询条件过滤正确
- [ ] 导出Excel功能正常
- [ ] 前端页面显示正常
- [ ] 权限控制生效

## 常见问题

### 1. 查询无数据

检查：
- 数据库表是否有数据
- 查询条件是否正确
- 表关联是否正确

### 2. 导出失败

检查：
- Excel导出依赖是否正确
- 文件权限是否足够
- 内存是否充足

### 3. 前端页面报错

检查：
- 路由配置是否正确
- 组件导入是否正确
- API接口是否可访问

## 维护说明

1. **数据同步**: 确保佣金计算数据及时更新
2. **性能优化**: 大数据量时考虑添加索引
3. **权限管理**: 定期检查权限配置
4. **日志监控**: 关注查询和导出操作的日志

## 联系支持

如有问题，请联系开发团队或查看相关文档。
