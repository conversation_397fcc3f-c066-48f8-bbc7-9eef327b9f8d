<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.crmfy.crmcommissioncalculation.mapper.CrmCommissionCalculationMapper">

    <!-- 分页查询佣金计算信息 -->
    <select id="queryCommissionCalculationInfo" resultType="org.jeecg.modules.crmfy.crmcommissioncalculation.vo.CrmCommissionCalculationQueryVO">
        SELECT
            calc.policy_no as policyNo,
            COALESCE(calc.calculation_date, pv.effective_date) as calculationDate,
            calc.plan_name as planName,
            calc.year_num as yearNum,
            calc.premium_amount as premiumAmount,
            calc.commission_amount as commissionAmount,
            calc.payment_currency as paymentCurrency,
            calc.contract_rate as contractRate,
            calc.commission_rate as commissionRate,
            calc.rc_exchange_rate as rcExchangeRate
        FROM crm_commission_calculation calc
        LEFT JOIN crm_policies p ON calc.policy_id = p.id
        LEFT JOIN crm_policy_versions pv ON p.id = pv.policy_id AND pv.iz_active = '1'
        WHERE calc.del_flag = 0
        <if test="query.policyNo != null and query.policyNo != ''">
            AND calc.policy_no LIKE CONCAT('%', #{query.policyNo}, '%')
        </if>
        <if test="query.companyCode != null and query.companyCode != ''">
            AND calc.company_code = #{query.companyCode}
        </if>
        <if test="query.yearNum != null">
            AND calc.year_num = #{query.yearNum}
        </if>
        <if test="query.calculationDateStart != null">
            AND COALESCE(calc.calculation_date, pv.effective_date) &gt;= #{query.calculationDateStart}
        </if>
        <if test="query.calculationDateEnd != null">
            AND COALESCE(calc.calculation_date, pv.effective_date) &lt;= #{query.calculationDateEnd}
        </if>
        ORDER BY COALESCE(calc.calculation_date, pv.effective_date) DESC, calc.policy_no, calc.year_num
    </select>

</mapper>