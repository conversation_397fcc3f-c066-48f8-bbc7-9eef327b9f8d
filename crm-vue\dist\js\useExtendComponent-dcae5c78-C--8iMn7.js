import{Q as Ai,h as Ci}from"./componentMap-Bg13cs1o.js";import{cl as Me,co as wi,O as yt,h as Tt,cp as xi,ac as Fi,cq as at,e as me,bD as Si,ah as kt,u as Pe,bl as It,aa as Oe,b8 as Ot,cr as Ti,a9 as Dt,f as Pt,b as Le,s as ki,F as oe,cs as Ii,ct as Oi,bb as Di,cu as Pi,aJ as Ri}from"./index-BnxvmHFv.js";import{e as pe,w as ge,u as Ae,f as k,r as Ce,J as be,n as Qe,ag as ie,aB as ke,ar as X,aq as de,as as $e,k as ce,d as Rt,l as je,aE as Mt,aD as ae,G as Fe,ah as Se,au as Te,h as Mi,c as Ei,o as Et,j as Bi,at as fe,F as Ve,aC as lt,ap as ji,ak as _i,aL as Qi}from"./vue-vendor-CnsIXr4W.js";import{p as He,aw as Ji,bz as Ni,bA as Li,B as Ze,b5 as $i,aN as rt,aV as Vi}from"./antd-vue-vendor-ac69AFxs.js";import{B as Bt}from"./index-j6u-UpGU.js";import"./index-D4YmpXsT.js";import{o as nt}from"./constant-fa63bd66-Ddbq-fz2.js";import"./index-C9Hr1YQz.js";import{d as Hi}from"./user.api-_jc-Tnyz.js";import{E as jt}from"./customExpression-CtXxD800.js";import"./index-DjTWLsmR.js";import{useListPage as Yi}from"./useListPage-BCVdKj1d.js";import Ui from"./LinkTableListPiece-f3a8e0d7-BR0-U9EN.js";import qi from"./OnlineSelectCascade-05c40fef-PZGR6IPg.js";import Ki from"./JModalTip-b055ab60-Co9Wp31o.js";import{U as Wi}from"./JUpload-DCci6z5o.js";import{u as _t,B as Qt}from"./useForm-DNLebgae.js";import zi from"./BasicTable-DO8Vk_Gm.js";import"./useFormItem-Eoh0MXq5.js";import"./index-BNka1IZr.js";import"./index-DmtYkh1u.js";import"./index-B4xK7O2u.js";import"./useTimeout-DoPujGLn.js";import"./vxe-table-vendor-CK0mysZD.js";import"./useIntersectionObserver-DZYoTRf_.js";import"./BasicModal-B-mPgnfO.js";import"./useWindowSizeFn-BcaCTZPc.js";import"./download-ZmOT1Dx2.js";import"./base64Conver-24EVOS6V.js";import"./index-DBQnQ9LG.js";import"./index-CUrp7h3O.js";import"./useCountdown-D_p0DKZc.js";import"./useFormItemSingle-D8EqzImO.js";import"./JSelectUser-Blw_A8N4.js";import"./props-DHLM7ils.js";import"./JSelectBiz-rbVGd4Dv.js";import"./JAddInput-B7aUDeJi.js";import"./areaDataUtil-ChdaTOUz.js";import"./index-86WEzeSw.js";import"./index-Dr3sBsWR.js";import"./bem-DfjdT9o5.js";import"./props-WP5sspMu.js";import"./useContextMenu-DwGGVBbH.js";import"./depart.api-BTFSMQ51.js";import"./JSelectDept-1TdcISOX.js";import"./JPopup-gJ1mXmQZ.js";import"./JEllipsis-IqMnhApY.js";import"./index-C3YRp4LI.js";import"./index-mK2t00gc.js";import"./JAreaLinkage-BDz8pfno.js";import"./JCodeEditor-C8R0_wo6.js";import"./htmlmixed-XbbCJteX.js";import"./vue-BLlfMlNv.js";/* empty css             */import"./EasyCronInput-BB8IcbzA.js";import"./injectionKey-DPVn4AgL.js";const Ye="data:image/png;base64,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";var Gi=Object.defineProperty,Zi=Object.defineProperties,Xi=Object.getOwnPropertyDescriptors,vt=Object.getOwnPropertySymbols,el=Object.prototype.hasOwnProperty,tl=Object.prototype.propertyIsEnumerable,At=(i,e,t)=>e in i?Gi(i,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):i[e]=t,he=(i,e)=>{for(var t in e||(e={}))el.call(e,t)&&At(i,t,e[t]);if(vt)for(var t of vt(e))tl.call(e,t)&&At(i,t,e[t]);return i},Je=(i,e)=>Zi(i,Xi(e)),W=(i,e,t)=>new Promise((l,r)=>{var s=f=>{try{w(t.next(f))}catch(T){r(T)}},u=f=>{try{w(t.throw(f))}catch(T){r(T)}},w=f=>f.done?l(f.value):Promise.resolve(f.value).then(s,u);w((t=t.apply(i,e)).next())});const il="jeecg_submit_form_and_flow",es="flow_submit_id",ll="online_form_table_name",Be="validate-failed",ts="setup",is="EnhanceJS",ls={password:"text",file:"text",image:"text",textarea:"text",umeditor:"text",markdown:"text",checkbox:"list_multi",radio:"list"},nl=".jeecg-online-modal .ant-modal-content",ol="online_";class Z{constructor(e,t){this._data=t,this.field=e,this.label=t.title,this.hidden=!1,this.order=t.order||999,this.required=!1,this.onlyValidator="",this.setFieldsValue="",this.hasChange=!0,e.indexOf("@")>0?this.pre=e.substring(0,e.indexOf("@")+1):this.pre="",this.schemaProp={},this.searchForm=!1,this.disabled=!1,this.popContainer="",this.handleWidgetAttr(t),this.inPopover=!1,this.labelLength=nt,this.initLabelLength()}getFormItemSchema(){let e=this.getItem();return this.addDefaultChangeEvent(e),e}getItem(){let e={field:this.field,label:this.label,labelLength:this.labelLength,component:"Input",itemProps:{labelCol:{class:"online-form-label"}}},t=this.getRule();return t.length>0&&this.onlyValidator&&(e.rules=t),this.hidden===!0&&(e.show=!1),e}setFormRef(e){this.formRef=e}isHidden(){return this.hidden=!0,this}isRequired(e){return e&&e.length>0&&e.indexOf(this.field)>=0&&(this.required=!0),this}initLabelLength(){let e=this.getExtendData();e&&e.labelLength&&(this.labelLength=e.labelLength)}getExtendData(){let e={},{fieldExtendJson:t}=this._data;if(t&&typeof t=="string")try{let l=JSON.parse(t);e=he({},l)}catch(l){}return e}getRelatedHideFields(){return[]}getPlaceholder(e){let t="请输入";return["list","radio","checkbox","date","datetime","time","list_multi","sel_search","popup","cat_tree","sel_depart","sel_user","pca","link_down","sel_tree","switch","link_table","link_table_field","popup_dict","LinkTableForQuery","CascaderPcaForQuery","select_user2","rangeDate","rangeTime","rangeNumber"].includes(e)?t="请选择":["file","image"].includes(e)&&(t="请上传"),t+this.label}setOnlyValidateFun(e){e&&(this.onlyValidator=(t,l)=>W(this,null,function*(){let r=yield e(t,l);return r?Promise.reject(r):Promise.resolve()}))}getRule(){let e=[];const{view:t,errorInfo:l,pattern:r,type:s,fieldExtendJson:u}=this._data;if(this.required===!0){let w=this.getPlaceholder(t);if(u){const f=JSON.parse(u);f.validateError&&(w=f.validateError)}l&&(w=l),t=="sel_depart"||t=="sel_user"?(this.schemaProp.required=!0,e.push({required:!0,message:w})):e.push({required:!0,message:w})}if(t=="sel_user"&&r==="only"&&this.onlyValidator&&e.push({validator:this.onlyValidator}),t==="list"||t==="radio"||t==="markdown"||t==="pca"||t.indexOf("sel")>=0||t==="time"||t.indexOf("upload")>=0||t.indexOf("file")>=0||t.indexOf("image")>=0)return e;if(r)if(r==="only")this.onlyValidator&&e.push({validator:this.onlyValidator});else if(r==="z")s=="number"||s=="integer"||e.push({pattern:/^-?\d+$/,message:"请输入整数"});else{let w=l||"正则校验失败",f;try{f=new RegExp(r),f||(f=r)}catch(T){f=r}e.push({pattern:f,message:w})}return e}addDefaultChangeEvent(e){this.hasChange&&(e.componentProps||(e.componentProps={}),this.disabled==!0&&(e.componentProps.disabled=!0),e.componentProps.hasOwnProperty("onChange")||(e.componentProps.onChange=(t,l)=>{t instanceof Event&&(t=t.target.value),t instanceof Array&&(t=t.join(",")),!this.formRef||!this.formRef.value||!this.formRef.value.$formValueChange||this.formRef.value.$formValueChange(this.field,t,l)})),Object.keys(this.schemaProp).map(t=>{e[t]=this.schemaProp[t]})}noChange(){this.hasChange=!1}updateField(e){this.field=e}setFunctionForFieldValue(e){e&&(this.setFieldsValue=e)}asSearchForm(){this.searchForm=!0}getModalAsContainer(){let e=this.getPopContainer();if(e!="body"){const t=document.querySelectorAll(e);if(t&&t.length>1){const l=[];if(t.forEach(r=>{r.offsetWidth==0&&r.offsetHeight==0||l.push(r)}),l.length===1)return l[0]}}return document.querySelector(e)}getPopContainer(){return this.searchForm===!0?"body":this.inPopover===!0?`.${this.popContainer}`:this.popContainer?`.${this.popContainer} .ant-modal-content`:nl}handleWidgetAttr(e){e.ui&&e.ui.widgetattrs&&e.ui.widgetattrs.disabled==!0&&(this.disabled=!0)}setCustomPopContainer(e){this.popContainer=e}getLinkFieldInfo(){return""}setOtherInfo(e){}isInPopover(){this.inPopover=!0}handleDictTableParams(){if(!this.formRef.value)return;const e=this._data.dictTable;if(!e)return;const t=e.match(/\${([^}]+)}/g);if(!t||t.length==0)return;const l=t.map(u=>u.replace("${","").replace("}","")),r=pe(()=>{const u=this.formRef.value.formModel;return l.map(w=>u[w]).join("")});let s=null;ge(r,()=>{s&&clearTimeout(s),s=setTimeout(()=>{const u=this.formRef.value.formModel;let w=e.replace(/\${([^}]+)}/g,(f,T)=>u[T]==null?"":u[T]);this.updateDictTable(w)},150)},{immediate:!0})}updateDictTable(e){}genDictTableCode(e,t,l){return e=wi(e),encodeURI(`${e},${t},${l}`)}}class Ct extends Z{getItem(){let e=super.getItem();return this.hidden===!0&&(e.show=!1),e}}var Jt=(i=>(i.datetime="YYYY-MM-DD HH:mm:ss",i.date="YYYY-MM-DD",i))(Jt||{});class sl extends Z{constructor(e,t,l){super(e,t),this.format=Jt[t.view],this.showTime=t.view!="date";let r=t.fieldExtendJson;t.view=="date"&&r&&(r=JSON.parse(r),r.picker&&r.picker!="default"?this.picker=r.picker:this.picker=void 0),this.allowSelectRange=["eq","ne"].includes(l==null?void 0:l.rule)}getItem(){let e=super.getItem();return Object.assign({},e,{component:"DatePickerInFilter",componentProps:{placeholder:`请选择${this.label}`,showTime:this.showTime,valueFormat:this.format,allowSelectRange:this.allowSelectRange,picker:this.picker,style:{width:"100%"},getPopupContainer:t=>this.getModalAsContainer()}})}}class al extends Z{constructor(e,t){super(e,t),this.schema=t,this.options=this.getOptions(t.enum,t.type),this.dictTable=t.dictTable,this.dictText=t.dictText,this.dictCode=t.dictCode,this.multi=t.multi||!1}getItem(){let e=super.getItem(),t=this.getFormComponent(),l=this.getComponentProps();return Object.assign({},e,{component:t,componentProps:l})}getFormComponent(){return this.options.length>0?"Select":"JDictSelectTag"}setFormRef(e){super.setFormRef(e),this.handleDictTableParams()}updateDictTable(e){this.formRef.value.updateSchema({field:this.field,componentProps:{dictCode:this.genDictTableCode(e,this.dictText,this.dictCode)}})}getComponentProps(){let e={allowClear:!0,mode:this.multi===!0?"multiple":"combobox",style:{width:"100%"},getPopupContainer:t=>this.getModalAsContainer(),onDropdownVisibleChange:t=>{t&&typeof this.schema.updateOptions=="function"&&this.schema.updateOptions()}};return this.options.length>0?e.options=this.options:this.dictTable?e.dictCode=this.genDictTableCode(this.dictTable,this.dictText,this.dictCode):(e.dictCode=this.dictCode,e.useDicColor=!0),e}getOptions(e,t){if(!e||e.length==0)return[];let l=t=="number",r=[];for(let s of e){if(s==null)break;let u=s.value;l&&(u=parseInt(u)),r.push(Je(he({},s),{value:u,label:s.title}))}return r}}class rl extends Z{getItem(){let e=super.getItem();return Object.assign({},e,{component:"InputPassword"})}}class ul extends Z{getItem(){let e=super.getItem(),t=this.getComponentProps();return Object.assign({},e,{component:"JUpload",componentProps:t})}getComponentProps(){let e=this.getExtendData();return e&&e.uploadnum?{maxCount:Number(e.uploadnum)}:{}}}class dl extends Z{getItem(){let e=super.getItem(),t=this.getComponentProps();return Object.assign({},e,{component:"JUpload",componentProps:t})}getComponentProps(){let e={fileType:Wi.image},t=this.getExtendData();return t&&t.uploadnum&&(e.maxCount=Number(t.uploadnum)),e}}class cl extends Z{getItem(){let e=super.getItem();return Object.assign({},e,{component:"InputTextArea",componentProps:{autoSize:{minRows:4,maxRows:10}}})}}class pl extends Z{constructor(e,t){super(e,t),this.dictTable=t.dictTable,this.dictText=t.dictText,this.dictCode=t.dictCode}setFormRef(e){super.setFormRef(e),this.handleDictTableParams()}updateDictTable(e){this.formRef.value.updateSchema({field:this.field,componentProps:{dictCode:this.genDictTableCode(e,this.dictText,this.dictCode)}})}getItem(){let e=super.getItem(),t=this.getComponentProps();return Object.assign({},e,{component:"JSelectMultiple",componentProps:t})}getComponentProps(){if(!this.dictTable&&!this.dictCode)return{};{let e={};return this.dictTable?e.dictCode=this.genDictTableCode(this.dictTable,this.dictText,this.dictCode):(e.dictCode=this.dictCode,e.useDicColor=!0),e.triggerChange=!0,e.popContainer=this.getPopContainer(),e}}}class hl extends Z{constructor(e,t){super(e,t),t.dictTable&&t.dictText&&t.dictCode?(this.dict=this.genDictTableCode(t.dictTable,t.dictText,t.dictCode),this.type=1):(this.dict=encodeURI(`${t.dictCode}`),this.type=0)}setFormRef(e){super.setFormRef(e),this.handleDictTableParams()}updateDictTable(e){this.formRef.value.updateSchema({field:this.field,componentProps:{dict:this.genDictTableCode(e,this._data.dictText,this._data.dictCode)}})}getItem(){let e=super.getItem(),t=this.getPopContainer();return Object.assign({},e,{component:"JSearchSelect",componentProps:{dict:this.dict,pageSize:10,async:!!this.type,popContainer:t}})}}class fl extends Z{constructor(e,t){super(e,t),this.code=t.code,this.multi=t.popupMulti,this.fieldConfig=this.getFieldConfig(t)}getItem(){let e=super.getItem(),t=this.getComponentProps();return Object.assign({},e,{component:"JPopup",componentProps:t})}getComponentProps(){let e={code:this.code,multi:this.multi,fieldConfig:this.fieldConfig};return this.formRef?e.formElRef=this.formRef:e.setFieldsValue=this.setFieldsValue,this.inPopover===!0&&(e.getContainer=()=>this.getModalAsContainer()),e.getFormValues=()=>Ae(this.formRef).getFieldsValue(),e}getFieldConfig(e){let{destFields:t,orgFields:l}=e;if(!t||t.length==0)return[];let r=t.split(","),s=l.split(","),u=[];const w=this.pre;for(let f=0;f<r.length;f++)u.push({target:w+r[f],source:s[f]});return u}}class ml extends Z{constructor(e,t){super(e,t),this.dictCode=`${t.code},${t.destFields},${t.orgFields}`,this.multi=t.popupMulti}getItem(){const e=super.getItem(),t=this.getComponentProps();return Object.assign({},e,{component:"JPopupDict",componentProps:t})}getComponentProps(){const e={dictCode:this.dictCode,multi:this.multi};return this.inPopover&&(e.getContainer=()=>this.getModalAsContainer()),e.getFormValues=()=>Ae(this.formRef).getFieldsValue(),e}}class gl extends Z{constructor(e,t){super(e,t),this.multi=!1,this.pid=t.pidValue,this.pcode=t.pcode,this.textField=t.textField}getItem(){let e=super.getItem(),t=this.getComponentProps();return Object.assign({},e,{componentProps:t,component:"JCategorySelect"})}getComponentProps(){let e={placeholder:"请选择"+this.label};if(this.pcode)e.pcode=this.pcode;else{let t=this.pid||"EMPTY_PID";e.pid=t}return this.textField?Je(he({loadTriggleChange:!0,multiple:this.multi},e),{back:this.textField,onChange:(t,l)=>{this.formRef&&(this.formRef.value.setFieldsValue(l),this.formRef.value.$formValueChange(this.field,t))}}):he({multiple:this.multi},e)}getRelatedHideFields(){let e=[];return this.textField&&e.push(this.textField),e}}class bl extends Z{getItem(){let e=super.getItem(),t=this.getComponentProps();return Object.assign({},e,{component:"JSelectDept",componentProps:t})}getComponentProps(){let e=this.getExtendData(),t={checkStrictly:!0,showButton:!1};return e.text&&(t.labelKey=e.text),e.store&&(t.rowKey=e.store),e.multiSelect===!1&&(t.multiple=!1),e.multiSelect===!0&&(t.multiple=!0),t.maxTagCount=3,this.inPopover===!0&&(t.getContainer=()=>this.getModalAsContainer()),t}}class yl extends Z{constructor(e,t){super(e,t),this.showButton=t.showButton!==!1}getItem(){let e=super.getItem(),t=this.getComponentProps();return Object.assign({},e,{component:"JSelectUser",componentProps:t})}getComponentProps(){let e=this.getExtendData(),t={showSelected:!1,allowClear:!0,isRadioSelection:!1,showButton:this.showButton};return e.text&&(t.labelKey=e.text),e.store&&(t.rowKey=e.store),e.multiSelect===!1&&(t.isRadioSelection=!0),t.maxTagCount=3,this.inPopover===!0&&(t.getContainer=()=>this.getModalAsContainer()),t}}class vl extends Z{getItem(){let e=super.getItem();return Object.assign({},e,{component:"JEditor",componentProps:{options:{auto_focus:!1}}})}}class Al extends Z{getItem(){let e=super.getItem();return Object.assign({},e,{component:"JMarkdownEditor",componentProps:{}})}}class Cl extends Z{getItem(){let e=super.getItem();return Object.assign({},e,{component:"JAreaLinkage",componentProps:{saveCode:"region",getPopupContainer:()=>document.querySelector("body")}})}}class wl extends Z{constructor(e,t){super(e,t),this.dict=t.dict,this.pidField=t.pidField,this.pidValue=t.pidValue,this.hasChildField=t.hasChildField}getItem(){let e=super.getItem();return Object.assign({},e,{component:"JTreeSelect",componentProps:{dict:this.dict,pidField:this.pidField,pidValue:this.pidValue,hasChildField:this.hasChildField}})}}class xl extends Z{constructor(e,t){super(e,t),this.dictTable=t.dictTable,this.dictText=t.dictText,this.dictCode=t.dictCode}setFormRef(e){super.setFormRef(e),this.handleDictTableParams()}updateDictTable(e){this.formRef.value.updateSchema({field:this.field,componentProps:{dictCode:this.genDictTableCode(e,this.dictText,this.dictCode)}})}getItem(){let e=super.getItem(),t=this.getComponentProps();return Object.assign({},e,{component:"JDictSelectTag",componentProps:t})}getComponentProps(){return!this.dictTable&&!this.dictCode?{}:this.dictTable?{dictCode:this.genDictTableCode(this.dictTable,this.dictText,this.dictCode),type:"radio"}:{useDicColor:!0,dictCode:this.dictCode,type:"radio"}}}class Fl extends Z{constructor(e,t){super(e,t),this.options=this.getOptions(t.enum)}setFormRef(e){super.setFormRef(e),this.handleDictTableParams()}updateDictTable(e){this.formRef.value.updateSchema({field:this.field,componentProps:{options:[],dictCode:this.genDictTableCode(e,this._data.dictText,this._data.dictCode)}})}getItem(){let e=super.getItem();return Object.assign({},e,{component:"JCheckbox",componentProps:{options:this.options,triggerChange:!0,useDicColor:!0}})}getOptions(e){if(!e||e.length==0)return[];let t=[];for(let l of e)t.push({value:l.value,label:l.title,color:l.color});return t}}class Sl extends Z{constructor(e,t){super(e,t)}getItem(){let e=super.getItem(),t=this.getComponentProps();return Object.assign({},e,{component:"JSwitch",componentProps:t})}getComponentProps(){let{fieldExtendJson:e}=this._data,t=["Y","N"];if(e&&typeof e=="string"){const l=JSON.parse(e);yt(l)&&l.length==2?t=l:Tt(l)&&yt(l.switchOptions)&&(t=l.switchOptions)}return{options:t}}}class Tl extends Z{getItem(){let e=super.getItem();return Object.assign({},e,{component:"TimePicker",componentProps:{placeholder:`请选择${this.label}`,valueFormat:"HH:mm:ss",getPopupContainer:t=>this.getModalAsContainer(),style:{width:"100%"}}})}}class kl extends Z{constructor(e,t){super(e,t);const{dictTable:l,dictText:r,dictCode:s,pidField:u,idField:w,origin:f,condition:T}=t;this.table=l,this.txt=r,this.store=s,this.idField=w,this.pidField=u,this.origin=f,this.condition=T,this.options=[],this.next=t.next||"",this.type=t.type}getItem(){let e=super.getItem(),t=this.getComponentProps();return Object.assign({},e,{component:"OnlineSelectCascade",componentProps:t})}getComponentProps(){let e={table:this.table,txt:this.txt,store:this.store,pidField:this.pidField,idField:this.idField,origin:this.origin,pidValue:"-1",style:{width:"100%"},onChange:t=>{this.valueChange(t)},onNext:t=>{this.nextOptionsChange(t)}};return this._data.origin===!0&&(e.condition=this.condition),e}nextOptionsChange(e){return W(this,null,function*(){!this.formRef||!this.next||(yield this.formRef.value.updateSchema({field:this.next,componentProps:{pidValue:e}}))})}valueChange(e){return W(this,null,function*(){if(!this.formRef)return;let t=this.formRef.value;t.$formValueChange(this.field,e),this.next&&(yield t.setFieldsValue({[this.next]:""}))})}}class wt extends Z{constructor(e,t){super(e,t),this.slot="";let l=t.fieldExtendJson;t.view=="date"&&l&&(l=JSON.parse(l),l.picker&&l.picker!="default"?this.picker=l.picker:this.picker=void 0),this.precision=t.dbPointLength}getItem(){let e=super.getItem(),t=this.slot;const l={};return this.picker&&(l.picker=this.picker),this.precision&&(l.precision=this.precision),Object.assign({},e,{slot:t,componentProps:l})}groupDate(){return this.slot="groupDate",this}groupDatetime(){return this.slot="groupDatetime",this}groupTime(){return this.slot="groupTime",this}groupNumber(){return this.slot="groupNumber",this}}class Il extends Z{constructor(e,t){super(e,t),this.dbPointLength=t.dbPointLength}getItem(){let e=super.getItem(),t=this.getComponentProps();return Object.assign({},e,{component:"InputNumber",componentProps:t})}getComponentProps(){const e={style:{width:"100%"}};return this.dbPointLength>=0&&(e.precision=this.dbPointLength),e}}class Ol extends Z{constructor(e,t){super(e,t),this.dictTable=t.dictTable,this.dictText=t.dictText,this.dictCode=t.dictCode,this.view=t.view,this.componentString="",this.linkFields=[]}getItem(){let e=super.getItem();const t=this.getComponentProps();return Object.assign({},e,{component:this.componentString,componentProps:t})}getComponentProps(){let e={textField:this.dictText,tableName:this.dictTable,valueField:this.dictCode},t=this.getExtendData();if(t.multiSelect?e.multi=!0:e.multi=!1,t.imageField?e.imageField=t.imageField:e.imageField="",t.showType=="select"){this.componentString="LinkTableSelect";let l=this.getPopContainer();e.popContainer=l}else this.componentString="LinkTableCard";return this.linkFields.length>0&&(e.linkFields=this.linkFields),e}setOtherInfo(e){this.linkFields=e}}class Dl extends Z{constructor(e,t){super(e,t),this.dictTable=t.dictTable,this.dictText=t.dictText}getItem(){let e=super.getItem();return Object.assign({},e,{componentProps:{readOnly:!0,allowClear:!1,disabled:!0,style:{background:"none",color:"rgba(0, 0, 0, 0.85)",border:"none"}}})}getLinkFieldInfo(){return[this.dictTable,`${this.field},${this.dictText}`]}}class Pl extends Z{constructor(e,t){super(e,t),this.code=t.code,this.titleField=t.titleField,this.multi=t.multi||!1}getItem(){let e=super.getItem();return Object.assign({},e,{component:"LinkTableForQuery",componentProps:{code:this.code,multi:this.multi,field:this.titleField,style:{width:"100%"}}})}}class Rl extends Z{constructor(e,t,l){var r;super(e,t),this.schema=t,this.areaLevel=(r=t.areaLevel)!=null?r:3,this.allowChangeLevel=["eq","ne"].includes(l==null?void 0:l.rule)}getItem(){let e=super.getItem();return Object.assign({},e,{component:"CascaderPcaInFilter",componentProps:{areaLevel:this.areaLevel,allowChangeLevel:this.allowChangeLevel,placeholder:"请选择…",style:{width:"100%"}}})}}class Ml extends Z{constructor(e,t){super(e,t),this.multi=t.multi===!0,this.store=t.store||"",this.query=t.query||!1}getItem(){let e=super.getItem(),t=this.getComponentProps();return Object.assign({},e,{component:"UserSelect",componentProps:t})}getComponentProps(){let e={multi:this.multi,store:this.store,query:this.query};return this.inPopover===!0&&(e.getContainer=()=>this.getModalAsContainer()),e}}class El extends Z{constructor(e,t){super(e,t);let l=t.view;this.format=t.format,this.datetime=!1,l==="rangeNumber"?this.componentType="JRangeNumber":l==="rangeTime"?this.componentType="RangeTime":(this.componentType="RangeDate",t.datetime===!0&&(this.datetime=!0))}getItem(){let e=super.getItem();return Object.assign({},e,{component:this.componentType,componentProps:{datetime:this.datetime,format:this.format,getPopupContainer:t=>this.getModalAsContainer()}})}}class Xe{static createFormSchema(e,t,l){switch(t.view){case"password":return new rl(e,t);case"list":return new al(e,t);case"radio":return new xl(e,t);case"checkbox":return new Fl(e,t);case"date":case"datetime":return new sl(e,t,l);case"time":return new Tl(e,t);case"file":return new ul(e,t);case"image":return new dl(e,t);case"textarea":return new cl(e,t);case"list_multi":return new pl(e,t);case"sel_search":return new hl(e,t);case"popup":return new fl(e,t);case"cat_tree":return new gl(e,t);case"sel_depart":return new bl(e,t);case"sel_user":return new yl(e,t);case"umeditor":return new vl(e,t);case"markdown":return new Al(e,t);case"pca":return new Cl(e,t);case"link_down":return new kl(e,t);case"sel_tree":return new wl(e,t);case"switch":return new Sl(e,t);case"link_table":return new Ol(e,t);case"link_table_field":return new Dl(e,t);case"popup_dict":return new ml(e,t);case"slot":return new wt(e,t);case"LinkTableForQuery":return new Pl(e,t);case"CascaderPcaForQuery":return new Rl(e,t,l);case"select_user2":return new Ml(e,t);case"rangeDate":case"rangeTime":case"rangeNumber":return new El(e,t);case"hidden":return new Ct(e,t).isHidden();default:return t.type=="number"?new Il(e,t):new Ct(e,t)}}static createSlotFormSchema(e,t){let l=new wt(e,t),r=t.view;if(r=="date")l.groupDate();else if(r=="datetime")l.groupDatetime();else if(r=="time")l.groupTime();else{let s=t.type;(s=="number"||s=="integer")&&l.groupNumber()}return l}static createIdField(){return{label:"",field:"id",component:"Input",show:!1}}}var ut=typeof globalThis!="undefined"?globalThis:typeof window!="undefined"?window:typeof global!="undefined"?global:typeof self!="undefined"?self:{};function dt(i){return i&&i.__esModule&&Object.prototype.hasOwnProperty.call(i,"default")?i.default:i}var Nt={exports:{}};(function(i,e){(function(t,l){i.exports=l()})(ut,function(){var t=1e3,l=6e4,r=36e5,s="millisecond",u="second",w="minute",f="hour",T="day",O="week",v="month",I="quarter",x="year",L="date",_="Invalid Date",Y=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/,J=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,B={name:"en",weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),ordinal:function(y){var a=["th","st","nd","rd"],o=y%100;return"["+y+(a[(o-20)%10]||a[o]||a[0])+"]"}},j=function(y,a,o){var h=String(y);return!h||h.length>=a?y:""+Array(a+1-h.length).join(o)+y},V={s:j,z:function(y){var a=-y.utcOffset(),o=Math.abs(a),h=Math.floor(o/60),n=o%60;return(a<=0?"+":"-")+j(h,2,"0")+":"+j(n,2,"0")},m:function y(a,o){if(a.date()<o.date())return-y(o,a);var h=12*(o.year()-a.year())+(o.month()-a.month()),n=a.clone().add(h,v),c=o-n<0,p=a.clone().add(h+(c?-1:1),v);return+(-(h+(o-n)/(c?n-p:p-n))||0)},a:function(y){return y<0?Math.ceil(y)||0:Math.floor(y)},p:function(y){return{M:v,y:x,w:O,d:T,D:L,h:f,m:w,s:u,ms:s,Q:I}[y]||String(y||"").toLowerCase().replace(/s$/,"")},u:function(y){return y===void 0}},z="en",K={};K[z]=B;var G=function(y){return y instanceof P},F=function y(a,o,h){var n;if(!a)return z;if(typeof a=="string"){var c=a.toLowerCase();K[c]&&(n=c),o&&(K[c]=o,n=c);var p=a.split("-");if(!n&&p.length>1)return y(p[0])}else{var S=a.name;K[S]=a,n=S}return!h&&n&&(z=n),n||!h&&z},A=function(y,a){if(G(y))return y.clone();var o=typeof a=="object"?a:{};return o.date=y,o.args=arguments,new P(o)},b=V;b.l=F,b.i=G,b.w=function(y,a){return A(y,{locale:a.$L,utc:a.$u,x:a.$x,$offset:a.$offset})};var P=function(){function y(o){this.$L=F(o.locale,null,!0),this.parse(o)}var a=y.prototype;return a.parse=function(o){this.$d=function(h){var n=h.date,c=h.utc;if(n===null)return new Date(NaN);if(b.u(n))return new Date;if(n instanceof Date)return new Date(n);if(typeof n=="string"&&!/Z$/i.test(n)){var p=n.match(Y);if(p){var S=p[2]-1||0,C=(p[7]||"0").substring(0,3);return c?new Date(Date.UTC(p[1],S,p[3]||1,p[4]||0,p[5]||0,p[6]||0,C)):new Date(p[1],S,p[3]||1,p[4]||0,p[5]||0,p[6]||0,C)}}return new Date(n)}(o),this.$x=o.x||{},this.init()},a.init=function(){var o=this.$d;this.$y=o.getFullYear(),this.$M=o.getMonth(),this.$D=o.getDate(),this.$W=o.getDay(),this.$H=o.getHours(),this.$m=o.getMinutes(),this.$s=o.getSeconds(),this.$ms=o.getMilliseconds()},a.$utils=function(){return b},a.isValid=function(){return this.$d.toString()!==_},a.isSame=function(o,h){var n=A(o);return this.startOf(h)<=n&&n<=this.endOf(h)},a.isAfter=function(o,h){return A(o)<this.startOf(h)},a.isBefore=function(o,h){return this.endOf(h)<A(o)},a.$g=function(o,h,n){return b.u(o)?this[h]:this.set(n,o)},a.unix=function(){return Math.floor(this.valueOf()/1e3)},a.valueOf=function(){return this.$d.getTime()},a.startOf=function(o,h){var n=this,c=!!b.u(h)||h,p=b.p(o),S=function(Q,q){var ee=b.w(n.$u?Date.UTC(n.$y,q,Q):new Date(n.$y,q,Q),n);return c?ee:ee.endOf(T)},C=function(Q,q){return b.w(n.toDate()[Q].apply(n.toDate("s"),(c?[0,0,0,0]:[23,59,59,999]).slice(q)),n)},g=this.$W,E=this.$M,M=this.$D,H="set"+(this.$u?"UTC":"");switch(p){case x:return c?S(1,0):S(31,11);case v:return c?S(1,E):S(0,E+1);case O:var N=this.$locale().weekStart||0,se=(g<N?g+7:g)-N;return S(c?M-se:M+(6-se),E);case T:case L:return C(H+"Hours",0);case f:return C(H+"Minutes",1);case w:return C(H+"Seconds",2);case u:return C(H+"Milliseconds",3);default:return this.clone()}},a.endOf=function(o){return this.startOf(o,!1)},a.$set=function(o,h){var n,c=b.p(o),p="set"+(this.$u?"UTC":""),S=(n={},n[T]=p+"Date",n[L]=p+"Date",n[v]=p+"Month",n[x]=p+"FullYear",n[f]=p+"Hours",n[w]=p+"Minutes",n[u]=p+"Seconds",n[s]=p+"Milliseconds",n)[c],C=c===T?this.$D+(h-this.$W):h;if(c===v||c===x){var g=this.clone().set(L,1);g.$d[S](C),g.init(),this.$d=g.set(L,Math.min(this.$D,g.daysInMonth())).$d}else S&&this.$d[S](C);return this.init(),this},a.set=function(o,h){return this.clone().$set(o,h)},a.get=function(o){return this[b.p(o)]()},a.add=function(o,h){var n,c=this;o=Number(o);var p=b.p(h),S=function(E){var M=A(c);return b.w(M.date(M.date()+Math.round(E*o)),c)};if(p===v)return this.set(v,this.$M+o);if(p===x)return this.set(x,this.$y+o);if(p===T)return S(1);if(p===O)return S(7);var C=(n={},n[w]=l,n[f]=r,n[u]=t,n)[p]||1,g=this.$d.getTime()+o*C;return b.w(g,this)},a.subtract=function(o,h){return this.add(-1*o,h)},a.format=function(o){var h=this,n=this.$locale();if(!this.isValid())return n.invalidDate||_;var c=o||"YYYY-MM-DDTHH:mm:ssZ",p=b.z(this),S=this.$H,C=this.$m,g=this.$M,E=n.weekdays,M=n.months,H=n.meridiem,N=function(q,ee,te,le){return q&&(q[ee]||q(h,c))||te[ee].slice(0,le)},se=function(q){return b.s(S%12||12,q,"0")},Q=H||function(q,ee,te){var le=q<12?"AM":"PM";return te?le.toLowerCase():le};return c.replace(J,function(q,ee){return ee||function(te){switch(te){case"YY":return String(h.$y).slice(-2);case"YYYY":return b.s(h.$y,4,"0");case"M":return g+1;case"MM":return b.s(g+1,2,"0");case"MMM":return N(n.monthsShort,g,M,3);case"MMMM":return N(M,g);case"D":return h.$D;case"DD":return b.s(h.$D,2,"0");case"d":return String(h.$W);case"dd":return N(n.weekdaysMin,h.$W,E,2);case"ddd":return N(n.weekdaysShort,h.$W,E,3);case"dddd":return E[h.$W];case"H":return String(S);case"HH":return b.s(S,2,"0");case"h":return se(1);case"hh":return se(2);case"a":return Q(S,C,!0);case"A":return Q(S,C,!1);case"m":return String(C);case"mm":return b.s(C,2,"0");case"s":return String(h.$s);case"ss":return b.s(h.$s,2,"0");case"SSS":return b.s(h.$ms,3,"0");case"Z":return p}return null}(q)||p.replace(":","")})},a.utcOffset=function(){return 15*-Math.round(this.$d.getTimezoneOffset()/15)},a.diff=function(o,h,n){var c,p=this,S=b.p(h),C=A(o),g=(C.utcOffset()-this.utcOffset())*l,E=this-C,M=function(){return b.m(p,C)};switch(S){case x:c=M()/12;break;case v:c=M();break;case I:c=M()/3;break;case O:c=(E-g)/6048e5;break;case T:c=(E-g)/864e5;break;case f:c=E/r;break;case w:c=E/l;break;case u:c=E/t;break;default:c=E}return n?c:b.a(c)},a.daysInMonth=function(){return this.endOf(v).$D},a.$locale=function(){return K[this.$L]},a.locale=function(o,h){if(!o)return this.$L;var n=this.clone(),c=F(o,h,!0);return c&&(n.$L=c),n},a.clone=function(){return b.w(this.$d,this)},a.toDate=function(){return new Date(this.valueOf())},a.toJSON=function(){return this.isValid()?this.toISOString():null},a.toISOString=function(){return this.$d.toISOString()},a.toString=function(){return this.$d.toUTCString()},y}(),D=P.prototype;return A.prototype=D,[["$ms",s],["$s",u],["$m",w],["$H",f],["$W",T],["$M",v],["$y",x],["$D",L]].forEach(function(y){D[y[1]]=function(a){return this.$g(a,y[0],y[1])}}),A.extend=function(y,a){return y.$i||(y(a,P,A),y.$i=!0),A},A.locale=F,A.isDayjs=G,A.unix=function(y){return A(1e3*y)},A.en=K[z],A.Ls=K,A.p={},A})})(Nt);var Bl=Nt.exports;const ve=dt(Bl);var Lt={exports:{}};(function(i,e){(function(t,l){i.exports=l()})(ut,function(){var t="week",l="year";return function(r,s,u){var w=s.prototype;w.week=function(f){if(f===void 0&&(f=null),f!==null)return this.add(7*(f-this.week()),"day");var T=this.$locale().yearStart||1;if(this.month()===11&&this.date()>25){var O=u(this).startOf(l).add(1,l).date(T),v=u(this).endOf(t);if(O.isBefore(v))return 1}var I=u(this).startOf(l).date(T).startOf(t).subtract(1,"millisecond"),x=this.diff(I,t,!0);return x<0?u(this).startOf("week").week():Math.ceil(x)},w.weeks=function(f){return f===void 0&&(f=null),this.week(f)}}})})(Lt);var jl=Lt.exports;const _l=dt(jl);var $t={exports:{}};(function(i,e){(function(t,l){i.exports=l()})(ut,function(){var t="month",l="quarter";return function(r,s){var u=s.prototype;u.quarter=function(T){return this.$utils().u(T)?Math.ceil((this.month()+1)/3):this.month(this.month()%3+3*(T-1))};var w=u.add;u.add=function(T,O){return T=Number(T),this.$utils().p(O)===l?this.add(3*T,t):w.bind(this)(T,O)};var f=u.startOf;u.startOf=function(T,O){var v=this.$utils(),I=!!v.u(O)||O;if(v.p(T)===l){var x=this.quarter()-1;return I?this.month(3*x).startOf(t).startOf("day"):this.month(3*x+2).endOf(t).endOf("day")}return f.bind(this)(T,O)}}})})($t);var Ql=$t.exports;const Jl=dt(Ql);ve.extend(_l);ve.extend(Jl);const Vt=Object.keys(jt),Nl=Vt.join(","),Ll=Vt.map(i=>jt[i]),Ht=/#{([^}]+)?}/g,Yt=/{{([^}]+)?}}/g,ot=/\${([^}]+)?}/g,_e={ADD:"add",EDIT:"edit",DETAIL:"detail",RELOAD:"reload"};function et(i,e,t){if(ct(e.defVal)){const l={field:i,type:e.type,value:e.defVal,view:e.view,fieldExtendJson:e.fieldExtendJson},r=t.findIndex(s=>s.field===i);r===-1?t.push(l):t[r]=l}}function $l(i,e){ct(i.fieldDefaultValue)&&e.push({field:i.key,type:i.type,value:i.fieldDefaultValue})}function st(i,e,t){return W(this,null,function*(){if(Array.isArray(i)&&i.length>0){let l={};for(let r of i){let{value:s,type:u,field:w}=r;s=yield Ut(s,_e.ADD,t||{}),u==="number"&&s&&(s=Number.parseFloat(s)),s=Vl(r,s),l[w]=s}e(l)}})}function Vl(i,e){const{type:t,field:l,view:r,fieldExtendJson:s}=i;if(r=="date"&&s){const u=JSON.parse(s),{picker:w}=u;if(w&&w!="default"&&e){let f;try{if(w==="year"){const T=e.split("-")[0];f=ve().year(T).format("YYYY-MM-DD")}if(w==="month"){const T=e.split("-"),O=T[0],v=+T[1]+1;f=ve().year(O).month(v).format("YYYY-MM-DD")}if(w==="week"){const T=e.split("-"),O=T[0],v=T[1].match(/^(\d+)周$/)[1];f=ve().year(O).week(v).format("YYYY-MM-DD")}if(w==="quarter"){const T=e.split("-"),O=T[0],v=T[1].match(/^[Qq](\d)$/)[1];f=ve().year(O).quarter(v).format("YYYY-MM-DD")}}catch(T){f=e}return f}return e}return e}function ns(i,e,t){return W(this,null,function*(){let{defVal:l,type:r}=e;if(ct(l)){let s=yield Ut(l,_e.ADD,{});if(r==="number"&&s)if(e.mode=="group"&&typeof s=="string"&&s.indexOf(",")!=-1){const u=s.split(",");s=[],u[0]&&s.push(Number.parseFloat(u[0])),u[1]&&s.push(Number.parseFloat(u[1]))}else s=Number.parseFloat(s);t[i]=s}})}function ct(i){return!!(i||i===0)}function Ut(i,e,t){return W(this,null,function*(){if(i!=null&&Hl(i)){let l=yield Ul(i,e,t);if(l!=null)return l}return i})}function Hl(i){let e=0,t=0,l=0;if(i.replace(ot,()=>l++),l>1)return!1;i.replace(Ht,()=>e++),i.replace(Yt,()=>t++);let r=e+t;return!(l>0&&r>0)}function Yl(i,e){let t=new Map;return i.replace(e,function(l,r){return t.set(l,r.trim()),l}),t}function Ul(i,e,t){return W(this,null,function*(){return(e===_e.ADD||e===_e.RELOAD)&&ot.test(i)?yield tt(i,ot,zl,[t]):e===_e.ADD?(i=yield tt(i,Ht,ql),i=yield tt(i,Yt,Wl),i):null})}function tt(i,e,t){return W(this,arguments,function*(l,r,s,u=[]){let w=Yl(l,r);for(let f of w.keys()){let T=w.get(f),O=yield s.apply(null,[T,f,...u]);if(f===l)return O;l=xi(l,f,O)}return l})}function ql(i,e){return W(this,null,function*(){switch(i){case"date":return ve().format("YYYY-MM-DD");case"time":return ve().format("HH:mm:ss");case"datetime":return ve().format("YYYY-MM-DD HH:mm:ss");default:let t=Kl(i);return t!=null?t:e}})}function Kl(i){let e=Fi().getUserInfo;if(e)switch(i){case"sysUserId":return e.id;case"sysUserCode":case"sys_user_code":return e.username;case"sysUserName":return e.realname;case"sysOrgCode":case"sys_org_code":return e.orgCode}return null}function Wl(i,e){return W(this,null,function*(){let t=at(`(function (${Nl}){ return ${i} })`);try{return t.apply(null,Ll)}catch(l){return e}})}function zl(i,e,t){return W(this,null,function*(){let l=`/sys/fillRule/executeRuleByCode/${i}`,r={};typeof t=="function"?r=t():t&&(r=he({},t));let{success:s,message:u,result:w}=yield me.put({url:l,params:r},{isTransformResponse:!1});return s?w:e})}const pt="link_down",Gl="link_table_field",Zl="link_table";function qt(i,e){zn();const t=i.modalClass,l=k([]),r=k(""),s=k({}),u=Ce({}),w=k(!1),f=k([]),T=k({}),O={},v=k([]),I=Ce({}),x=k("");x.value={sm:24,xs:24,md:12,lg:12,xl:12,xxl:12};const L=k({xs:{span:24},sm:{span:4},md:{span:4},lg:{span:4},xl:{span:4},xxl:{span:4}}),_=k(null),Y=k(6*14+10);function J(F,A,b,P={}){var D;tn(I),I[r.value]=[];let y=[],a=[],o=[],h={},n={};Object.keys(F).map(g=>{var E;const M=F[g];if(M.view=="tab"){w.value=!0,I[g]=[];let H={key:g,foreignKey:M.foreignKey,describe:M.describe,relationType:M.relationType,requiredFields:M.required||[],order:M.order,id:M.id};M.relationType==1?(O[g]=k(null),H.properties=M.properties):(B(M),O[g]=k(),H.columns=M.columns,h[g]=[]),y.push(H),nn(g,M)}else if(et(g,M,I[r.value]),M.view===pt){let H=en(M,g);for(let N of H){const se=N.key==g?M:(E=M.others)==null?void 0:E.find(ee=>ee.field===N.key);se&&et(N.key,se,I[r.value]),u[N.key]=!0,u[N.key+"_load"]=!0,u[N.key+"_disabled"]=!1,j(P,N);let Q=Xe.createFormSchema(N.key,N);b&&Q.setOnlyValidateFun(b),Q.isRequired(A),Q.setFormRef(e),Q.handleWidgetAttr(M);let q=xt(a,N.key);q==-1?a.push(Q):a[q]=Q}}else if(et(g,M,I[r.value]),u[g]=!0,u[g+"_load"]=!0,u[g+"_disabled"]=!1,xt(a,g)==-1){j(P,M);let H=Xe.createFormSchema(g,M);if(b&&H.setOnlyValidateFun(b),H.isRequired(A),H.setFormRef(e),a.push(H),o.push(...H.getRelatedHideFields()),M.view===Gl){let N=H.getLinkFieldInfo();N&&(n[N[0]]?n[N[0]].push(N[1]):n[N[0]]=[N[1]])}}}),a.sort(function(g,E){return g.order-E.order});const c=[];(()=>{for(let g=0,E=a.length;g<E;g++){const M=a[g];V(M==null?void 0:M._data,"isOneRow")&&(c.push(a.splice(g,1)[0]),g--,E--)}})(),a=[...a,...c];let p=[];p.push(Xe.createIdField());let S=null,C=!1;for(let g of a){const E=g.label.length;S?(S.label.length<E||S.label.length===E&&!S.required&&g.required)&&(S=g):S=g,g.required&&(C=!0),g.view&&g.view==Zl&&n[g.field]&&g.setOtherInfo(n[g.field]),o.indexOf(g.field)>=0&&g.isHidden(),t&&g.setCustomPopContainer(t);const M=g.getFormItemSchema();if(M.component==="JDictSelectTag"&&((D=g==null?void 0:g._data)==null?void 0:D.type)==="number"&&(M.componentProps.stringToNumber=!0),i.formTemplate>1&&V(g==null?void 0:g._data,"isOneRow")){M.colProps={span:24};const H=z(),{labelCol:N={}}=H,se={},Q={};Object.keys(N).forEach(q=>{if(["xs","sm","md","lg","xl","xxl"].includes(q)){const ee=N[q].span,te=Math.round(ee/i.formTemplate);se[q]={span:te},Q[q]={span:24-te-1}}}),M.itemProps={labelCol:se,wrapperCol:Q}}p.push(M)}if(l.value=p,y.sort(function(g,E){return g.order-E.order}),y.forEach(g=>{const E=g.columns;g.columns&&E.forEach(M=>{var H;if(g.relationType==0&&["popup","popup_dict"].includes(M.type)){let N=!0;M.fieldExtendJson&&(N=JSON.parse(M.fieldExtendJson).popupMulti);const se=(H=M.props)!=null?H:{};M.props=Je(he({},se),{multi:N})}if(M.type==="date"&&M.fieldExtendJson){const N=JSON.parse(M.fieldExtendJson);N.picker&&N.picker!="default"&&Object.assign(M,{picker:N.picker})}})}),f.value=y,T.value=h,P.formLabelLengthShow&&P.formLabelLength)Y.value=P.formLabelLength*14+10+ +`${C?13:0}`,_.value=null;else if(S){let g=S.label.length;g=g>nt?nt:g;const E=S.required,M=g*14+10+ +`${E?13:0}`;Y.value=M}}ge(u,F=>{let A=e.value,b=[],P=be(F);Object.keys(P).map(D=>{if(!D.endsWith("_load")){let y={field:D,show:P[D]},a=D+"_load";P.hasOwnProperty(a)&&(y.ifShow=P[a]);let o=D+"_disabled";P.hasOwnProperty(o)&&(y.dynamicDisabled=()=>P[o]),b.push(y)}}),A&&A.updateSchema(b)},{immediate:!1});function B(F){Kt(F,A=>{$l(A,I[F.key])})}function j(F,A,b="labelLength"){const{formLabelLengthShow:P,formLabelLength:D}=F;if(P&&D){let y=A==null?void 0:A.fieldExtendJson;y?(y=JSON.parse(y),y[b]=D):y={[b]:D},A.fieldExtendJson=JSON.stringify(y)}}function V(F={},A){let b=F==null?void 0:F.fieldExtendJson;if(b)return b=JSON.parse(b),b[A]}ge(()=>i.formTemplate,()=>{const F=z();x.value=F.baseColProps,L.value=F.labelCol,_.value=F.wrapperCol},{immediate:!0});function z(){let F=i.formTemplate;return F==2?{baseColProps:{sm:24,xs:24,md:12,lg:12,xl:12,xxl:12}}:F==3?{baseColProps:{sm:24,xs:24,md:8,lg:8,xl:8,xxl:8}}:F==4?{baseColProps:{sm:24,xs:24,md:6,lg:6,xl:6,xxl:6}}:{baseColProps:{sm:24,xs:24,md:24,lg:24,xl:24,xxl:24}}}function K(F,A){return new Promise(b=>{A||b("");let P={tableName:r.value.replace(/\$\d+/,""),fieldName:F.field,fieldVal:A},D=s.value;D.id&&(P.dataId=D.id),Hi(P).then(y=>{y.success?b(""):b(y.message)}).catch(y=>{b(y)})})}function G(F){return Object.keys(F).map(A=>{F[A]&&F[A]instanceof Array&&(F[A]=F[A].join(","))}),F}return{formSchemas:l,defaultValueFields:I,tableName:r,dbData:s,checkOnlyFieldValue:K,createFormSchemas:J,fieldDisplayStatus:u,subTabInfo:f,hasSubTable:w,subDataSource:T,baseColProps:x,changeDataIfArray2String:G,linkDownList:v,refMap:O,labelCol:L,wrapperCol:_,labelWidth:Y}}function Kt(i,e){const t={inputNumber:"input-number",sel_depart:"depart-select",sel_user:"user-select",list_multi:"select-multiple",input_pop:"textarea",sel_search:"select-search","select-dict-search":"selectDictSearch"};i.columns.forEach(s=>{s.type==="radio"?s.type="select":t[s.type]?s.type=t[s.type]:s.type==="popup"&&l(s),s.type==="depart-select"&&(s.checkStrictly=!0),s.type==="user-select"&&r(s),s.type==="pca"&&(s.width="230px"),(s.width==120||s.width=="120px")&&(s.type=="image"||s.type=="file")&&(s.width="130px"),s.width||(s.width="200px"),e&&e(s)});function l(s){let{destFields:u,orgFields:w}=s,f=[];if(!(!u||u.length==0)){let T=u.split(","),O=w.split(",");for(let v=0;v<T.length;v++)f.push({target:T[v],source:O[v]})}s.fieldConfig=f}function r(s){let u=s.fieldExtendJson,w=!1;if(u)try{JSON.parse(u).multiSelect===!1&&(w=!0)}catch(f){}s.isRadioSelection=w}}function Xl(){let i={};const e={addSubRows:"<m> 一对多子表，新增自定义行",changeOptions:"<m> 改变下拉框选项",clearSubRows:"<m> 清空一对多子表行",clearThenAddRows:"<m> 清空一对多子表行，然后新增自定义行",executeMainFillRule:"<m> 刷新主表的增值规制值",executeSubFillRule:"<m> 刷新子表的增值规制值",getFieldsValue:"<m> 获取表单控件的值",getSubTableInstance:"<m> 获取子表实例",isUpdate:"<p> 判断是否为编辑模式",loading:"<p> 页面加载状态",onlineFormRef:"<p> 当前表单ref对象",refMap:"<p> 子表ref对象map",setFieldsValue:"<m> 设置表单控件的值",sh:"<p> 表单控件的显示隐藏状态",subActiveKey:"<p> 子表激活tab，对应子表表名",subFormHeight:"<p> 一对一子表表单高度",submitFlowFlag:"<p> 是否提交流程状态",subTableHeight:"<p> 一对多子表表格高度",tableName:"<p> 当前表名",triggleChangeValues:"<m> 修改多个表单值",triggleChangeValue:"<m> 修改表单值",updateSchema:"<m> 修改表单控件配置",changeSubTableOptions:"<m> 改变一对多子表下拉框选项",changeSubFormbleOptions:"<m> 改变一对一子表下拉框选项",changeRemoteOptions:"<m> 改变远程下拉框选项",submitFormAndFlow:"<m> 提交表单且发起流程"},t=new Proxy(e,{get(s,u){return Reflect.get(i,u)}});function l(s,u){i[s]=u}function r(s){Object.keys(s).map(u=>{i[u]=s[u]})}return l("$nextTick",Qe),l("addObject2Context",l),{onlineFormContext:t,addObject2Context:l,resetContext:r}}function en(i,e){const{config:{table:t,key:l,txt:r,linkField:s,idField:u,pidField:w,condition:f},others:T,order:O,title:v}=i;let I={dictTable:t,dictText:r,dictCode:l,pidField:w,idField:u,view:pt,type:i.type},x=[],L=he({key:e,title:v,order:O,condition:f,origin:!0},I);if(s&&s.length>0){let _=s.split(",");L.next=_[0];for(let Y=0;Y<_.length;Y++)for(let J of T)if(J.field==_[Y]){let B=he({key:J.field,title:J.title,order:J.order,origin:!1},I);Y+1<_.length&&(B.next=_[Y+1]),x.push(B)}}return x.push(L),x}function xt(i,e){let t=-1;for(let l=0;l<i.length;l++)if(i[l].field===e){t=l;break}return t}function Re(i){return new Promise(e=>{(function t(){let l=i.value;l?e(l):setTimeout(()=>{t()},100)})()})}function tn(i){Object.keys(i).map(e=>{delete i[e]})}const ln=Si();function nn(i,e){let t=e.hideButtons,l=ol+i+":";t||(t=[]),ln.setOnlineSubTableAuth(l,t)}function os(i){const e=k([]),t={},l=Ce({}),r=k(!1),s=k([]),u=k({}),{getIsMobile:w}=kt(),f=pe(()=>{let x=i.formTemplate;return w.value?24:x=="2"?12:x=="3"?8:x=="4"?6:24});function T(x){let L=[],_=[],Y={};Object.keys(x).map(J=>{const B=x[J];if(B.view=="tab"){r.value=!0;let j={key:J,foreignKey:B.foreignKey,describe:B.describe,relationType:B.relationType,requiredFields:B.required||[],order:B.order};B.relationType==1?(t[J]=k(null),j.properties=B.properties):(O(B),t[J]=k(),j.columns=B.columns,Y[J]=[],l[J]=!1),L.push(j)}else if(B.view===pt){let j=I(B,J);for(let V of j){let z=v(_,V.key),K={field:V.key,label:V.title,view:V.view,order:V.order,dictTable:V.dictTable,linkField:V.linkField||""};z==-1?_.push(K):_[z]=K}}else if(B.view!="hidden"&&v(_,J)==-1){let j=Object.assign({field:J,label:B.title},He(B,["view","order","fieldExtendJson","dictTable","dictText","dictCode","dict"]));if(B.view=="file"&&(j.span=24,j.isFile=!0),B.view=="image"&&(j.span=24,j.isImage=!0),B.view=="link_table"&&B.fieldExtendJson)try{let V=JSON.parse(B.fieldExtendJson);V.showType!="select"&&(j.isCard=!0),V.multiSelect==!0&&(j.multi=!0)}catch(V){}(B.view=="umeditor"||B.view=="markdown")&&(j.isHtml=!0,j.span=24),_.push(j)}}),_.sort(function(J,B){return J.order-B.order}),L.sort(function(J,B){return J.order-B.order}),s.value=L;for(let J=0;J<_.length;J++){let B=_[J];if((B.isFile===!0||B.isImage===!0||B.isHtml===!0)&&J>0){let j=_[J-1],V=j.span||f.value;j.span=V}}e.value=_,u.value=Y}function O(x){Kt(x)}function v(x,L){let _=-1;for(let Y=0;Y<x.length;Y++)if(x[Y].field===L){_=Y;break}return _}function I(x,L){let _=[];const{config:{table:Y,key:J,txt:B,linkField:j},order:V,title:z,others:K}=x;let G={view:"link_down",order:V,title:z,dictTable:JSON.stringify({table:Y,key:J,txt:B})};if(_.push(Object.assign({},{linkField:j,key:L},G)),j){let F=j.split(",");for(let A of F){let b="";for(let P of K)P.field==A&&(b=P.title);_.push(Object.assign({},{key:A},G,{title:b}))}}return _}return{detailFormSchemas:e,hasSubTable:r,subTabInfo:s,refMap:t,showStatus:l,createFormSchemas:T,formSpan:f,subDataSource:u}}function on(i,e=!0){let t=Ce({});const l=(v,I)=>me.get({url:v,params:I},{isTransformResponse:!1}),r=(v,I)=>me.post({url:v,params:I},{isTransformResponse:!1}),s=(v,I)=>me.put({url:v,params:I},{isTransformResponse:!1}),u=(v,I)=>me.delete({url:v,params:I},{isTransformResponse:!1});e===!0?(i._getAction=l,i._postAction=r,i._putAction=s,i._deleteAction=u,i._useMessage=Pe):(i.addObject2Context("_getAction",l),i.addObject2Context("_postAction",r),i.addObject2Context("_putAction",s),i.addObject2Context("_deleteAction",u),i.addObject2Context("_useMessage",Pe));function w(v){if(v){let I,x;try{I=at(v),x=new I(l,r,u)}catch(L){x={};const{createMessage:_}=Pe();_.warning(`js增强代码有语法错误，请检查代码~ ${L}`)}return x}else return{}}function f(v,I){t&&t[I]&&t[I](v)}function T(v,I){return t&&t.beforeSubmit?t.beforeSubmit(v,I):Promise.resolve()}function O(v,I){return t&&t.beforeDelete?t.beforeDelete(v,I):Promise.resolve()}return e===!0&&i&&(i.beforeDelete=v=>{const I=i.EnhanceJS;return I&&I.beforeDelete?I.beforeDelete(i,v):Promise.resolve()},i.beforeEdit=v=>{const I=i.EnhanceJS;return I&&I.beforeEdit?I.beforeEdit(i,v):Promise.resolve()}),{EnhanceJS:t,initCgEnhanceJs:w,customBeforeSubmit:T,beforeDelete:O,triggerJsFun:f}}const sn="/online/cgform/api/subform",an={name:"OnlineSubForm",components:{BasicForm:Qt,Loading:It},props:{properties:{type:Object,required:!0},mainId:{type:String,default:""},table:{type:String,default:""},formTemplate:{type:Number,default:1},requiredFields:{type:Array,default:[]},isUpdate:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1}},emits:["formChange"],setup(i,{emit:e}){const t=k(null),l=k(!1);Pe();const{formSchemas:r,defaultValueFields:s,changeDataIfArray2String:u,tableName:w,dbData:f,checkOnlyFieldValue:T,fieldDisplayStatus:O,createFormSchemas:v,baseColProps:I,labelCol:x,wrapperCol:L,labelWidth:_}=qt(i,t),[Y,{setProps:J,validate:B,resetFields:j,setFieldsValue:V,getFieldsValue:z,updateSchema:K,scrollToField:G}]=_t({schemas:r,showActionButtonGroup:!1,baseColProps:I,labelWidth:_,labelCol:x,wrapperCol:L});ge(()=>i.table,()=>{w.value=i.table},{immediate:!0}),ge(()=>i.properties,n=>{l.value=!1,F(),v(i.properties,i.requiredFields,T),l.value=!0},{deep:!0,immediate:!0}),ge(()=>i.mainId,n=>{setTimeout(()=>{b()},100)},{immediate:!0}),ge(()=>i.disabled,n=>{J({disabled:n})});function F(){return W(this,null,function*(){let n=yield Re(t);n.$formValueChange=(c,p)=>{let S={[c]:p};e("formChange",S)}})}function A(){if(Ae(i.isUpdate)===!1){let n=be(s[w.value]);st(n,c=>{V(c)})}}function b(){return W(this,null,function*(){yield Re(l),yield j(),A();const{table:n,mainId:c}=i;if(!n||!c)return;let p=yield P(n,c);f.value=p,yield V(p)})}function P(n,c){let p=`${sn}/${n}/${c}`;return new Promise((S,C)=>{me.get({url:p},{isTransformResponse:!1}).then(g=>{g.success?S(g.result):C()})}).finally(()=>{f.value=""})}function D(){return new Promise((n,c)=>{B().then(()=>{let p=z();p=u(p),n(p)}).catch(p=>{p.errorFields&&(p.scrollToField=()=>p.errorFields[0]&&G(p.errorFields[0].name,{behavior:"smooth",block:"center"})),c(p)})})}function y(){let n=z();return n.id||(n.id="sub-change-temp-id"),{row:n,target:h}}function a(n){V(n)}function o(){let n=z(),c=be(s[w.value]);st(c,p=>{V(p)},n)}const h={onlineFormRef:t,baseColProps:I,formSchemas:r,registerForm:Y,setFieldsValue:V,getFieldsValue:z,getFormEvent:y,setValues:a,getAll:D,executeFillRule:o,sh:O,resetFields:j,updateSchema:K};return h}};function rn(i,e,t,l,r,s){const u=ie("BasicForm");return X(),ke(u,{ref:"onlineFormRef",onRegister:i.registerForm},null,8,["onRegister"])}const Wt=Me(an,[["render",rn],["__scopeId","data-v-196bf574"]]),ss=Object.freeze(Object.defineProperty({__proto__:null,default:Wt},Symbol.toStringTag,{value:"Module"})),it={optPre:"/online/cgform/api/form/",urlButtonAction:"/online/cgform/api/doButton"},un={name:"OnlinePopForm",components:{BasicForm:Qt,Loading:It,OnlineSubForm:Wt,PrinterOutlined:Li,DiffOutlined:Ni,FormOutlined:Ji},props:{id:{type:String,default:""},formTemplate:{type:Number,default:1},disabled:{type:Boolean,default:!1},isTree:{type:Boolean,default:!1},pidField:{type:String,default:""},submitTip:{type:Boolean,default:!0},modalClass:{type:String,default:""},request:{type:Boolean,default:!0},isVxeTableData:{type:Boolean,default:!1}},emits:["success","rendered","dataChange"],setup(i,{emit:e}){const{createMessage:t}=Pe(),[l,{openModal:r}]=Oe(),s=k(""),u=k(null),w=k(!0),f=k(!1),T=k(1),O=k(!1),v=k(!1),I=Ce({reportPrintShow:0,reportPrintUrl:"",joinQuery:0,modelFullscreen:0,modalMinWidth:""}),{onlineFormContext:x,resetContext:L}=Xl(),{formSchemas:_,defaultValueFields:Y,changeDataIfArray2String:J,tableName:B,dbData:j,checkOnlyFieldValue:V,hasSubTable:z,subTabInfo:K,refMap:G,subDataSource:F,baseColProps:A,createFormSchemas:b,fieldDisplayStatus:P,labelCol:D,wrapperCol:y,labelWidth:a}=qt(i,u);let{EnhanceJS:o,initCgEnhanceJs:h}=on(x,!1);const[n,{setProps:c,validate:p,resetFields:S,setFieldsValue:C,updateSchema:g,getFieldsValue:E,scrollToField:M}]=_t({schemas:_,showActionButtonGroup:!1,baseColProps:A,labelWidth:a,labelCol:D,wrapperCol:y}),H=k(!1);function N(){let d=i.disabled;H.value=d,c({disabled:d})}function se(d,m,R){return W(this,null,function*(){yield S(),j.value="";let $=Ae(d);v.value=$,$&&(yield q(m)),yield Qe(()=>{!$&&R&&C(R),Q(),ze("js","loaded"),N()})})}function Q(){if(Ae(v)===!1){let d=be(Y[B.value]);st(d,m=>{C(m)})}}function q(d){return W(this,null,function*(){let m=yield te(d.id);(!m||Object.keys(m).length==0)&&(m=he({},be(d))),j.value=Object.assign({},m);let R=ee.value,$=He(m,...R);i.isVxeTableData===!0&&($=Object.assign({},$,d)),yield C($)})}let ee=pe(()=>{let d=_.value,m=[];for(let R of d)m.push(R.field);return m});function te(d){let m=`${it.optPre}${i.id}/${d}`;return new Promise((R,$)=>{me.get({url:m},{isTransformResponse:!1}).then(ne=>{ne.success?R(ne.result):($(),t.warning(ne.message))}).catch(()=>{$()})})}function le(d){return W(this,null,function*(){T.value=d.head.tableType,B.value=d.head.tableName,w.value=d.head.tableType==1,qe(d.head.extConfigJson),b(d.schema.properties,d.schema.required,V,I),o=h(d.enhanceJs),e("rendered",I);let m=yield Re(u);m.$formValueChange=(R,$,ne)=>{ri(R,$),ne&&C(ne)}})}function qe(d){let m={reportPrintShow:0,reportPrintUrl:"",joinQuery:0,modelFullscreen:1,modalMinWidth:"",formLabelLength:null};d&&(m=JSON.parse(d)),Object.keys(m).map(R=>{I[R]=m[R]})}function Ke(){w.value===!0?Ie():U()}function U(){re().then(d=>{ue(d)})}function re(){let d={};return new Promise((m,R)=>{p().then($=>m($),({errorFields:$})=>{R({code:Be,key:B.value,scrollToField:()=>$[0]&&M($[0].name,{behavior:"smooth",block:"center"})})})}).then(m=>(Object.assign(d,J(m)),De())).then(m=>(Object.assign(d,m),Promise.resolve(d))).catch(m=>((m===Be||(m==null?void 0:m.code)===Be)&&(t.warning("校验未通过"),m.key&&(ye(m.key),m.scrollToField&&setTimeout(()=>m.scrollToField(),150))),Promise.reject(null)))}function ye(d){let m=K.value;for(let R=0;R<m.length;R++)if(d==m[R].key){Ee.value=R+"";break}}function De(){return new Promise((d,m)=>W(this,null,function*(){let R={};try{let $=K.value;for(let ne=0;ne<$.length;ne++){let we=$[ne].key,xe=G[we].value;if(xe instanceof Array&&(xe=xe[0]),$[ne].relationType==1)try{let Ge=yield xe.getAll();R[we]=[],R[we].push(Ge)}catch(Ge){return m(he({code:Be,key:we},Ge))}else{if(yield xe.fullValidateTable())return m({code:Be,key:we});R[we]=xe.getTableData()}}}catch($){m($)}d(R)}))}function Ie(){return W(this,null,function*(){try{let d=yield p();d=Object.assign({},j.value,d),d=J(d),f.value=!0,ue(d)}catch(d){if(Tt(d)){const m=d.errorFields;m!=null&&m.length&&m[0].errors&&(t.warning(m[0].errors[0]),M(m[0].name,{behavior:"smooth",block:"center"}))}}finally{f.value=!1}})}function ue(d){ci(bt,d).then(()=>{yi(d)}).catch(m=>{t.warning(m)})}function Ne(d,m,R){m&&R?R.setValues?R.setValues(d):R.setValues([{rowKey:m,values:d}]):C(d)}function We(d,m){let R={};R[d]=m,C(R)}const Ee=k("0"),ht=k(300),ft=k(340);function ii(d){if(v.value===!0){let m=j.value;return li(m,d)}return""}function li(d,m){if(d){let R=d[m];return!R&&R!==0&&(R=d[m.toLowerCase()],!R&&R!==0&&(R=d[m.toUpperCase()])),R}return""}function ni(d,m){if(o&&o[m+"_onlChange"]){let R=o[m+"_onlChange"](),$=Object.keys(d)[0];if(R[$]){let ne=G[m].value;ne instanceof Array&&(ne=ne[0]);let we=ne.getFormEvent(),xe=he({column:{key:$},value:d[$]},we);R[$].call(x,x,xe)}}}function oi(d,m){if(o&&o[m+"_onlChange"]){let R=o[m+"_onlChange"](x);R[d.column.key]&&R[d.column.key].call(x,x,d)}}function si(d,m){}function ai(d){return"online_"+d+":"}function ri(d,m){return W(this,null,function*(){if(j.value[d]!=m&&e("dataChange",d),!o||!o.onlChange||!d)return!1;let R=o.onlChange();if(R[d]){let $={row:yield E(),column:{key:d},value:m};R[d].call(x,x,$)}})}function ze(d,m){if(d=="js")o&&o[m]&&o[m].call(x,x);else if(d=="action"){let R=j.value,$={formId:i.id,buttonCode:m,dataId:R.id,uiFormData:Object.assign({},R)};me.post({url:`${it.urlButtonAction}`,params:$},{isTransformResponse:!1}).then(ne=>{ne.success?t.success("处理完成!"):t.warning("处理失败!")})}}function mt(d){let m=G[d].value,R=[...m.getNewDataWithId(),...F.value[d]];if(!R||R.length==0)return!1;let $=[];for(let ne of R)$.push(ne.id);m.removeRowsById($)}function gt(d,m){if(!m)return!1;let R=G[d].value;typeof m=="object"?R.addRows(m,!0):t.error("添加子表数据,参数不识别!")}function ui(d,m){mt(d),gt(d,m)}function di(d,m){!m&&m.length<=0&&(m=[]),m.map(R=>{R.hasOwnProperty("label")||(R.label=R.text)}),g({field:d,componentProps:{options:m}})}function ci(d,m){return o&&o.beforeSubmit?o.beforeSubmit(d,m):Promise.resolve()}function pi(d,m){let R=be(P);d&&d.length>0?Object.keys(R).map($=>{!$.endsWith("_load")&&d.indexOf($)<0&&(P[$]=!1)}):m&&m.length>0&&Object.keys(R).map($=>{m.indexOf($)>=0&&(P[$]=!1)})}function hi(d){return W(this,null,function*(){yield S(),j.value="",v.value=!0,yield q(d),yield Qe(()=>{ze("js","loaded")})})}function fi(d){let m=G[d].value;return m instanceof Array&&(m=m[0]),m}function mi(){let d=I.reportPrintUrl,m=j.value.id,R=Ot();Ti(d,m,R)}function gi(d){s.value=d.id,r(!0)}function bi(d){}function yi(d){if(Object.keys(d).map(m=>{Array.isArray(d[m])&&d[m].length==0&&(d[m]="")}),i.request==!1)e("success",d);else{let m=`${it.optPre}${i.id}?tabletype=${T.value}`;O.value===!0&&(d[il]=1);let R=v.value===!0?"put":"post";me.request({url:m,method:R,params:d},{isTransformResponse:!1}).then($=>{$.success?($.result&&(d.id||(d.id=$.result)),e("success",d),j.value=d,v.value=!0,t.success("操作成功!")):t.warning($.message)}).finally(()=>{f.value=!1})}}function vi(){return W(this,null,function*(){let d=j.value,m=ee.value,R=He(d,...m);if(d)yield C(R);else{let $={};for(let ne of m)$[ne]="";yield C($)}})}let bt={tableName:B,loading:f,subActiveKey:Ee,onlineFormRef:u,getFieldsValue:E,setFieldsValue:C,submitFlowFlag:O,subFormHeight:ht,subTableHeight:ft,refMap:G,triggleChangeValues:Ne,triggleChangeValue:We,sh:P,clearSubRows:mt,addSubRows:gt,clearThenAddRows:ui,changeOptions:di,isUpdate:v,getSubTableInstance:fi};return L(bt),{tableName:B,onlineFormRef:u,registerForm:n,loading:f,subActiveKey:Ee,hasSubTable:z,subTabInfo:K,refMap:G,subFormHeight:ht,getSubTableForeignKeyValue:ii,isUpdate:v,handleSubFormChange:ni,subTableHeight:ft,onlineFormDisabled:H,subDataSource:F,getSubTableAuthPre:ai,handleAdded:si,handleValueChange:oi,openSubFormModalForAdd:gi,openSubFormModalForEdit:bi,registerVxeFormModal:l,vxeTableId:s,show:se,createRootProperties:le,handleSubmit:Ke,sh:P,handleCgButtonClick:ze,handleCustomFormSh:pi,handleCustomFormEdit:hi,dbData:j,onOpenReportPrint:mi,onlineExtConfigJson:I,recoverFormData:vi}}},dn=["id"];function cn(i,e,t,l,r,s){const u=ie("BasicForm");return X(),de("div",{id:l.tableName+"_form",class:$e(["onlinePopFormWrap",[`formTemplate_${t.formTemplate}`]])},[ce(u,{ref:"onlineFormRef",onRegister:l.registerForm},null,8,["onRegister"])],10,dn)}const zt=Me(un,[["render",cn],["__scopeId","data-v-2c2d5706"]]),as=Object.freeze(Object.defineProperty({__proto__:null,default:zt},Symbol.toStringTag,{value:"Module"}));function pn(i,{emit:e}={},t){const l=k(null),r=k(!1),s=k(1),u=k([]),w=k(!1),f=k(0),T=k(!1),O=k(""),v=k(!1),I=k(!1),x=k(!0),L=Ce({}),_=k(!0),Y=k(""),J=k(!0),B=k(!1),{popModalFixedWidth:j,resetBodyStyle:V,popBodyStyle:z}=Gt(),K=k(!1),G=k(""),{getIsMobile:F}=kt(),A={handleOpenModal:U=>{}},b=k(""),P=k(""),D=k(""),y=k(!1);let a={};const o=pe(()=>Y.value||(Ae(r)===!0?"详情":Ae(I)===!0?"编辑":"新增")),[h,{setModalProps:n,closeModal:c}]=Dt(U=>W(this,null,function*(){Y.value="",B.value=!1,i===!0?yield A.handleOpenModal(U):yield C(U),V(),t&&t()})),p=k(!1);function S(){return W(this,null,function*(){return yield Re(p),K.value})}function C(U){return W(this,null,function*(){n({confirmLoading:!1}),I.value=U.isUpdate,r.value=U.disableSubmit||!1,(U==null?void 0:U.hideSub)===!0&&(_.value=!1),U!=null&&U.title&&(Y.value=U.title),U!=null&&U.record?D.value=U.record.id:D.value="",yield Qe(()=>W(this,null,function*(){yield Re(w),E(),yield l.value.show(U==null?void 0:U.isUpdate,U==null?void 0:U.record,U==null?void 0:U.param)}))})}function g(U){w.value=!0,f.value=U.modalMinWidth,U.modelFullscreen==1?n({defaultFullscreen:!0}):n({defaultFullscreen:!1}),a=U,F.value&&(a.commentStatus=0)}function E(){let U=D.value;a.commentStatus==1&&U?(y.value=!0,n({defaultFullscreen:!0})):y.value=!1}const M=800,H=1100,N=pe(()=>{let U=200*(s.value-1),re=(Ae(x)?M:H)+U;re=se(re);let ye=f.value;return ye&&re<ye&&(re=ye),re});function se(U){let re=L.modalMinWidth;if(re!=null&&re!=="")try{if(re=Number.parseInt(re),U<re)return re}catch(ye){}return U}function Q(U,re){l.value.handleCgButtonClick(U,re)}function q(){v.value=!0,setTimeout(()=>{v.value=!1},1500),l.value.handleSubmit()}function ee(){c()}function te(U,re={}){let ye=`/online/cgform/api/getFormItem/${U}`;return new Promise((De,Ie)=>{me.get({url:ye,params:re},{isTransformResponse:!1}).then(ue=>{ue.success?De(ue.result):Ie(ue.message)}).catch(()=>{Ie()})})}function le(U,re,ye,De,Ie){return W(this,null,function*(){let ue=null;if(De&&Ie){const We=`/online/cgform/api/getFormItemBytbname/${Ie}`,Ee={taskId:De};ue=yield me.get({url:We,params:Ee})}else ue=yield te(U,re);let Ne=ue.head.formTemplate;s.value=Ne?Number(Ne):1,u.value=ue.cgButtonList,T.value=ue.head.isTree==="Y",O.value=ue.head.treeParentIdField||"",b.value=ue.head.id,P.value=ue.head.tableName,G.value=ue.head.themeTemplate,ue.form_disable_update===!0?K.value=!0:K.value=!1,p.value=!0,e&&e("formConfig",ue),ye&&ye(ue),yield Qe(()=>W(this,null,function*(){yield(yield Re(l)).createRootProperties(ue)}))})}function qe(U){U[ll]=P.value,e("success",U),J.value==!0&&c(),B.value=!1,J.value=!0}function Ke(){l.value&&l.value.onCloseModal(),I.value&&(a!=null?a:{}).commentStatus==1&&n({defaultFullscreen:!1})}return{title:o,modalWidth:N,registerModal:h,closeModal:c,modalObject:A,onCloseEvent:Ke,cgButtonList:u,handleCgButtonClick:Q,disableSubmit:r,handleSubmit:q,submitLoading:v,handleCancel:ee,successThenClose:J,handleSuccess:qe,topTipVisible:B,handleFormConfig:le,onlineFormCompRef:l,formTemplate:s,isTreeForm:T,pidFieldName:O,renderSuccess:g,formRendered:w,isUpdate:I,showSub:_,themeTemplate:G,tableId:b,tableName:P,formDataId:D,enableComment:y,popBodyStyle:z,popModalFixedWidth:j,getFormStatus:S}}function Gt(){const i=k(800);let e=window.innerWidth-300;e<800&&(e=800),i.value=e;const t=k({});function l(){let r=window.innerHeight-210;t.value={height:r+"px",overflowY:"auto"}}return{popModalFixedWidth:i,popBodyStyle:t,resetBodyStyle:l}}const hn=Rt({name:"OnlinePopModal",props:{id:{type:String,default:""},showFields:{type:Array,default:()=>[]},hideFields:{type:Array,default:()=>[]},topTip:{type:Boolean,default:!1},request:{type:Boolean,default:!0},saveClose:{type:Boolean,default:!1},isVxeTableData:{type:Boolean,default:!1},formTableType:{type:String,default:""},taskId:{type:String},tableName:{type:String}},components:{BasicModal:Bt,OnlinePopForm:zt,JModalTip:Ki,Button:Ze},emits:["success","register","formConfig"],setup(i,{emit:e}){const{title:t,registerModal:l,cgButtonList:r,handleCgButtonClick:s,disableSubmit:u,handleSubmit:w,submitLoading:f,handleCancel:T,handleFormConfig:O,onlineFormCompRef:v,formTemplate:I,isTreeForm:x,pidFieldName:L,renderSuccess:_,formRendered:Y,handleSuccess:J,topTipVisible:B,successThenClose:j,isUpdate:V,popBodyStyle:z,popModalFixedWidth:K,getFormStatus:G}=pn(!1,{emit:e});ge(()=>i.id,F,{immediate:!0});function F(){return W(this,null,function*(){if(Y.value=!1,!i.id)return;let a={};i.formTableType&&(a.tabletype=i.formTableType),i.taskId?yield O(i.id,a,null,i.taskId,i.tableName):yield O(i.id,a)})}function A(){i.saveClose===!1&&(j.value=!1),w()}function b(){B.value=!1,v.value.recoverFormData()}function P(){B.value=!0}const D=pe(()=>V.value?i.topTip:!1),y=pe(()=>{if(V.value==!0)return null;{let a=f.value;return[je(Ze,{type:"primary",loading:a,onClick:w},()=>"确定"),je(Ze,{onClick:T},()=>"关闭")]}});return{title:t,topTipVisible:B,handleSaveData:A,handleRecover:b,onlineFormCompRef:v,renderSuccess:_,registerModal:l,handleSubmit:w,handleSuccess:J,handleCancel:T,formTemplate:I,disableSubmit:u,cgButtonList:r,handleCgButtonClick:s,isTreeForm:x,pidFieldName:L,submitLoading:f,handleDataChange:P,isUpdate:V,showTopTip:D,modalFooter:y,popBodyStyle:z,popModalFixedWidth:K,getFormStatus:G}}});function fn(i,e,t,l,r,s){const u=ie("j-modal-tip"),w=ie("online-pop-form"),f=ie("BasicModal");return X(),ke(f,Mt({width:i.popModalFixedWidth,dialogStyle:{top:"70px"},bodyStyle:i.popBodyStyle},i.$attrs,{footer:i.modalFooter,cancelText:"关闭",onRegister:i.registerModal,wrapClassName:"jeecg-online-pop-modal",onOk:i.handleSubmit}),{title:ae(()=>[Fe(Te(i.title)+" ",1),i.showTopTip?(X(),ke(u,{key:0,visible:i.topTipVisible,onSave:i.handleSaveData,onCancel:i.handleRecover},null,8,["visible","onSave","onCancel"])):Se("",!0)]),default:ae(()=>[ce(w,{ref:"onlineFormCompRef",id:i.id,disabled:i.disableSubmit,"form-template":i.formTemplate,isTree:i.isTreeForm,pidField:i.pidFieldName,request:i.request,isVxeTableData:i.isVxeTableData,onRendered:i.renderSuccess,onSuccess:i.handleSuccess,onDataChange:i.handleDataChange,"modal-class":"jeecg-online-pop-modal"},null,8,["id","disabled","form-template","isTree","pidField","request","isVxeTableData","onRendered","onSuccess","onDataChange"])]),_:1},16,["width","bodyStyle","footer","onRegister","onOk"])}const Ue=Me(hn,[["render",fn]]),rs=Object.freeze(Object.defineProperty({__proto__:null,default:Ue},Symbol.toStringTag,{value:"Module"}));function Ft(i,e){const t="/online/cgform/api/getData/"+i;return me.get({url:t,params:e})}function mn(i,e){const t="/online/cgform/api/getColumns/"+i;return me.get({url:t,params:e})}function Zt(i){const e=k("1"),t=k({}),l=k({}),r=k(""),s=Ce({add:!0,update:!0}),u=pe(()=>i.textField?i.textField.split(","):[]),w=k([]),f=pe(()=>{let F=w.value;return i.multi==!0?F.slice(0,3):F.slice(0,6)});Mi(()=>W(this,null,function*(){if(i.tableName){let F=i.valueField||"",A=i.textField||"",b=[];if(F&&b.push(F),A){let D=A.split(",");r.value=D[0];for(let y of D)b.push(y)}let P=i.imageField||"";P&&b.push(P),t.value={linkTableSelectFields:b.join(",")},yield x(),yield L()}}));const T=pe(()=>{let F=i.textField||"",A=[],b="";if(F){let P=F.split(",");b=P[0];for(let D=0;D<P.length;D++)D>0&&A.push(P[D])}return{others:A,labelField:b}}),O=k([]),v=k([]),I=k({});function x(){return W(this,null,function*(){let F=t.value;const A=yield mn(i.tableName,F);if(v.value=A.columns,A.columns){let b=i.imageField,P=A.columns.filter(D=>D.dataIndex!=r.value&&D.dataIndex!=b);w.value=P}if(I.value=A.dictOptions,A.hideColumns){let b=A.hideColumns;b.indexOf("add")>=0?s.add=!1:s.add=!0,b.indexOf("update")>=0?s.update=!1:s.update=!0}})}function L(){return W(this,null,function*(){let F=Y(),A=(yield Ft(i.tableName,F)).records,b=[],{others:P,labelField:D}=T.value,y=i.imageField;if(A&&A.length>0)for(let a of A){let o=he({},a);_(o);let h=Object.assign({},He(o,P),{id:o.id,label:o[D],value:o[i.valueField]});y&&(h[y]=o[y]),b.push(h)}i.editBtnShow&&b.push({}),O.value=b})}function _(F){let A=v.value,b=I.value;for(let P of A){const{dataIndex:D,customRender:y}=P;if((F[D]||F[D]===0)&&y&&y==D&&b[y]){F[D]=Pt(b[y],F[D]);continue}let a=F[D+"_dictText"];a&&(F[D]=a)}}function Y(){return Object.assign({pageSize:100,pageNo:e.value},t.value,l.value)}function J(F){if(!F)l.value={};else{let A=u.value,b=[],P=[];for(let D=0;D<A.length;D++)D<=1&&(P.push(A[D]),b.push({field:A[D],rule:"like",val:F}));b.superQueryMatchType="or",b.superQueryParams=encodeURI(JSON.stringify(b)),l.value=b}}function B(F){return W(this,null,function*(){if(!F)return[];let A=i.valueField,b=Je(he({},t.value),{pageSize:100,pageNo:e.value});b.superQueryMatchType="and";let P=[{field:A,rule:"in",val:F}];b.superQueryParams=encodeURI(JSON.stringify(P));let D=(yield Ft(i.tableName,b)).records,y=[];if(D&&D.length>0)for(let a of D){let o=he({},a);_(o),y.push(o)}return y})}function j(F,A){if(!F||F.length==0)return!1;let b=A.split(",");if(b.length!=F.length)return!1;let P=!0;for(let D of F){let y=D[i.valueField];b.indexOf(y)<0&&(P=!1)}return P}function V(F){Object.keys(F).map(A=>{F[A]instanceof Array&&(F[A]=F[A].join(","))})}function z(F,A,b){if(b||(b={}),A&&A.length>0)for(let P of A){let D=P.split(","),y=D[0],a=D[1];if(F[y])F[y].push(b[a]);else{let o=b[a]||"";F[y]=[o]}}}function K(F){if(i.imageField){let A=F[i.imageField];return typeof A=="string"&&(A=A.split(",")[0]),Le(A)}return""}const G=pe(()=>!!i.imageField);return{pageNo:e,otherColumns:w,realShowColumns:f,selectOptions:O,reloadTableLinkOptions:L,textFieldArray:u,addQueryParams:J,tableColumns:v,transData:_,mainContentField:r,loadOne:B,compareData:j,formatData:V,initFormData:z,getImageSrc:K,showImage:G,auths:s}}const gn={name:"LinkTableSelect",components:{PlusOutlined:rt,EditOutlined:$i,OnlinePopModal:Ue},props:{valueField:oe.string.def(""),textField:oe.string.def(""),tableName:oe.string.def(""),multi:oe.bool.def(!1),value:oe.oneOfType([oe.string,oe.number,oe.array]),linkFields:oe.array.def([]),imageField:oe.string.def(""),editBtnShow:oe.bool.def(!0)},emits:["change","update:value"],setup(i,{emit:e,attrs:t}){const l=Ei("tableId",k(null)),r=k(),s=k([]),{auths:u,mainContentField:w,textFieldArray:f,selectOptions:T,reloadTableLinkOptions:O,addQueryParams:v,formatData:I,initFormData:x,getImageSrc:L,showImage:_}=Zt(i),[Y,{openModal:J}]=Oe(),B=pe(()=>i.tableName),j=pe(()=>i.multi===!0?Je(he({},t),{mode:"multiple"}):he({},t));function V(a){a==null||a.stopPropagation(),a==null||a.preventDefault(),J(!0,{})}function z(a,o){a==null||a.stopPropagation(),a==null||a.preventDefault(),u.update!=!1&&J(!0,{isUpdate:!0,record:o})}const K="custom:online:reload";Et(()=>{r.value&&r.value.addEventListener(K,G)}),Bi(()=>{r.value&&r.value.removeEventListener(K,G)});function G(){O()}function F(a){return W(this,null,function*(){try{const h=document.querySelectorAll(`.online-list-${l.value} .jeecg-basic-table-form-container.online-query-form .link-table-select-box`);h&&h.length>0&&h.forEach(n=>n.dispatchEvent(new Event(K)))}catch(h){}yield O();let o=a[i.valueField];i.multi===!0?s.value=[o]:s.value=o,b(s.value)})}function A(a){v(a),O()}function b(a){P(a),a||(v(),O())}function P(a){let o={},h=i.linkFields,n=[];if(!a)x(o,h);else{let c=be(T.value),p=be(a);p instanceof Array?n=[...p]:i.multi==!0?n=p.split(","):n=[p];let S=c.filter(C=>n.indexOf(C[i.valueField])>=0);if(S&&S.length>0){let C=he({},S[0]);if(S.length>1)for(let E=1;E<S.length;E++)C=D(C,S[E]);let g=w.value;C[g]=C.label,x(o,h,C)}}I(o),e("change",n.join(",")||"",o),e("update:value",n.join(",")||"")}function D(a,o){let h={};return Object.keys(a).map(n=>{h[n]=(a[n]||"")+","+(o[n]||"")}),h}ge(()=>i.value,a=>W(this,null,function*(){a?(i.multi==!0?s.value=a.split(","):s.value=a,i.linkFields&&i.linkFields.length>0&&P(a)):s.value=[]}),{immediate:!0}),ge(()=>T.value,a=>{a&&a.length>0&&i.linkFields&&i.linkFields.length>0&&s.value&&s.value.length>0&&P(s.value)});const y=a=>{a.target.src=Ye};return{boxRef:r,selectValue:s,selectOptions:T,registerPopModal:Y,popTableName:B,textFieldArray:f,handleClickAdd:V,handleClickEdit:z,getFormData:F,handleSearch:ki(A,800),handleChange:b,bindValue:j,showImage:_,getImageSrc:L,auths:u,placeholderImage:Ye,handleImageError:y}}},bn={class:"link-table-select-box",ref:"boxRef"},yn={key:1,class:"online-select-item"},vn={key:0,class:"left-avatar"},An=["src"],Cn=["src"],wn={class:"right-content"},xn={class:"others"},Fn={class:"other-item ellipsis"};function Sn(i,e,t,l,r,s){const u=ie("PlusOutlined"),w=ie("EditOutlined"),f=ie("a-select"),T=ie("online-pop-modal");return X(),de("div",bn,[ce(f,Mt({value:l.selectValue,"onUpdate:value":e[2]||(e[2]=O=>l.selectValue=O),style:{width:"100%"},placeholder:"请选择","option-label-prop":"label",popupClassName:"table-link-select",allowClear:"","show-search":""},l.bindValue,{options:l.selectOptions,"filter-option":!1,"not-found-content":null,onSearch:l.handleSearch,onChange:l.handleChange}),{option:ae(O=>[!O.value&&l.auths.add?(X(),de("div",{key:0,class:"opt-add",onClick:e[0]||(e[0]=(...v)=>l.handleClickAdd&&l.handleClickAdd(...v))},[ce(u),Fe(" 记录 ")])):(X(),de("div",yn,[l.showImage?(X(),de("div",vn,[l.getImageSrc(O)?(X(),de("img",{key:0,src:l.getImageSrc(O),alt:"",onError:e[1]||(e[1]=(...v)=>l.handleImageError&&l.handleImageError(...v))},null,40,An)):(X(),de("img",{key:1,src:l.placeholderImage,alt:""},null,8,Cn))])):Se("",!0),fe("div",wn,[fe("div",{class:$e(["label",{noEditBtn:!(t.editBtnShow&&l.auths.update)}])},[t.editBtnShow&&l.auths.update?(X(),ke(w,{key:0,onClick:v=>l.handleClickEdit(v,O)},null,8,["onClick"])):Se("",!0),Fe(" "+Te(O.label),1)],2),fe("div",xn,[(X(!0),de(Ve,null,lt(l.textFieldArray,v=>(X(),de("div",Fn,Te(O[v]),1))),256))])])]))]),_:1},16,["value","options","onSearch","onChange"]),ce(T,{id:l.popTableName,onRegister:l.registerPopModal,onSuccess:l.getFormData,topTip:""},null,8,["id","onRegister","onSuccess"])],512)}const Xt=Me(gn,[["render",Sn],["__scopeId","data-v-76bee333"]]),us=Object.freeze(Object.defineProperty({__proto__:null,default:Xt},Symbol.toStringTag,{value:"Module"}));function Tn(i,e){let t=ji();const l=k([]),r=k({}),s=k([]),u=k(null);let w=k(!0),f=pe(()=>{if(w.value!=!0)return{x:!1}});const[T,{openModal:O}]=Oe(),v=k(""),[I,{openModal:x}]=Oe(),L=k("");function _(o,h="checkbox"){r.value=o.dictOptions,o.checkboxFlag=="Y"?u.value={selectedRowKeys:s,onChange:Y,type:h}:u.value=null,w.value=o.scrollFlag==1;let n=o.columns;n.forEach(C=>{var g;if(C.fieldExtendJson&&JSON.parse(C.fieldExtendJson).isFixed&&(C.fixed="left"),C.hrefSlotName&&C.scopedSlots){const E=(g=o.fieldHrefSlots)==null?void 0:g.find(M=>M.slotName===C.hrefSlotName);E&&(C.fieldHref=E)}Object.keys(C).map(E=>{C[E]==null&&delete C[E]})});let c=o.fieldHrefSlots;const p={};c.forEach(C=>p[C.slotName]=C);let S=[];if(S=J(n,p),G(S),i.isTree()===!0){let C=o.textField,g=-1;for(let E=0;E<S.length;E++)if(S[E].dataIndex==C){g=E;break}if(g>0){let E=S.splice(g,1);S.unshift(E[0])}S.length>0&&(S[0].align="left")}l.value=S,i.reloadTable()}function Y(o,h){s.value=o,i.selectedRows=be(h),i.selectedRowKeys=be(o)}function J(o,h){for(let n of o){let{customRender:c,hrefSlotName:p,fieldType:S}=n;if(S=="date"||S=="Date")n.customRender=({text:C})=>C?C.length>10?C.substring(0,10):C:"";else if(S=="link_table")n.customRender=({text:C,record:g})=>{if(!C)return"";if(i.isPopList===!0)return g[n.dataIndex+"_dictText"];{let E=(C+"").split(","),M=[];g[n.dataIndex+"_dictText"]&&(M=g[n.dataIndex+"_dictText"].split(","));let H=[];for(let N=0;N<E.length;N++){let se=je(Ui,{id:E[N],text:M[N],onTab:Q=>a(Q,p)});H.push(se)}return H.length==0?"":je("div",{style:{overflow:"hidden"}},H)}};else if(S==="popup_dict")n.customRender=({text:C,record:g})=>g[n.dataIndex+"_dictText"]!=null?g[n.dataIndex+"_dictText"]:C;else{if(!p&&n.scopedSlots&&n.scopedSlots.customRender&&h.hasOwnProperty(n.scopedSlots.customRender)&&(p=n.scopedSlots.customRender),c||p){let C=c,g="_replace_text_";n.ellipsis=!0,n.customRender=({text:E,record:M})=>{let H=E;if(C)if(C.startsWith(g)){let N=C.replace(g,"");H=M[N]}else H=Pt(Ae(r)[C],E+"");if(n.showLength&&H&&H.length>n.showLength&&(H=H.substr(0,n.showLength)+"..."),p){let N=h[p];if(N)return je("a",{onClick:()=>B(N,M)},H)}return H}}if(n.scopedSlots){n.ellipsis=!0;let C=n.scopedSlots;n.slots=C,delete n.scopedSlots}}}return o}function B(o,h){let n=o.href,c=/(ht|f)tp(s?)\:\/\/[0-9a-zA-Z]([-.\w]*[0-9a-zA-Z])*(:(0-9)*)*(\/?)([a-zA-Z0-9\-\.\?\,\'\/\\\+&amp;%\$#_]*)?/,p=/\.vue(\?.*)?$/,S=/{{([^}]+)}}/g;if(typeof n=="string")if(n.startsWith("ONLINE:")){let C=n.split(":");v.value=C[1];let g=C[2];O(!0,{isUpdate:!0,disableSubmit:!0,hideSub:!0,record:{id:h[g]}})}else n=n.trim().replace(/\${([^}]+)?}/g,(C,g)=>h[g]),S.test(n)&&(n=n.replace(S,function(C,g){try{return g.trim()==="ACCESS_TOKEN"?Ot():at(g)}catch(E){return C}})),c.test(n)?window.open(n,"_blank"):p.test(n)?V(n):t.push(n)}const j=Ce({model:{title:"",okText:"关闭",width:"100%",open:!1,destroyOnClose:!0,style:{top:0,left:0,height:"100%",margin:0,padding:0},bodyStyle:{padding:"8px",height:"calc(100vh - 108px)",overflow:"auto",overflowX:"hidden"},cancelButtonProps:{style:{display:"none"}}},on:{ok:()=>j.model.open=!1,cancel:()=>j.model.open=!1},is:null,params:{}});function V(o){let h=o.indexOf("?"),n=o;if(h!==-1){n=o.substring(0,h);let c=o.substring(h+1,o.length).split("&"),p={};c.forEach(S=>{let C=S.split("=");p[C[0]]=C[1]}),j.params=p}else j.params={};j.model.open=!0,j.model.title="操作",j.is=_i(Qi(()=>Ii(n)))}let z="left";i.isTree()&&(z="right");const K=Ce({title:"操作",dataIndex:"action",slots:{customRender:"action"},fixed:z,align:"center",width:150});ge(()=>e==null?void 0:e.value,()=>{var o,h;((o=e==null?void 0:e.value)==null?void 0:o.tableFixedAction)===1&&(K.fixed=((h=e==null?void 0:e.value)==null?void 0:h.tableFixedActionType)||"right",i.isTree()&&(K.fixed="right"))});function G(o){let h=!1;for(let n=0;n<o.length;n++)if(o[n].dataIndex.toLowerCase()=="bpm_status"){h=!0;break}return i.hasBpmStatus=h,h}function F(o,h,n,c){if(o)if(o.indexOf(",")>0)Oi(`/online/cgform/field/download/${c}/${h.id}/${n.dataIndex}`,`文件_${h.id}.zip`);else{const p=Le(o);window.open(p)}}function A(o){return o&&o.indexOf(",")>0&&(o=o.substring(0,o.indexOf(","))),Le(o)}function b(o){return o?Di(o):""}function P(o,h){if(!o)return"";let n=o;n.length>10&&(n=n.substring(0,10));let c=h==null?void 0:h.fieldExtendJson;return c&&(c=JSON.parse(c),c.picker&&c.picker!="default")?Pi(n)[c.picker]:n}ge(s,()=>{i.selectedRowKeys=be(s.value)}),i.clearSelectedRow=()=>{s.value=[],i.selectedRows=[],i.selectedRowKeys=[]};function D(o){if(o){let h=[],n=o.split(",");for(let c of n)c&&h.push(Le(c));Ri({imageList:h})}}const y=k();function a(o,h){return W(this,null,function*(){L.value=h,(yield y.value.getFormStatus())==!0?(v.value=h,O(!0,{isUpdate:!0,disableSubmit:!0,hideSub:!0,record:{id:o}})):x(!0,{isUpdate:!0,record:{id:o}})})}return{columns:l,actionColumn:K,selectedKeys:s,rowSelection:u,enableScrollBar:w,tableScroll:f,downloadRowFile:F,getImgView:A,getPcaText:b,getFormatDate:P,handleColumnResult:_,onSelectChange:Y,hrefComponent:j,viewOnlineCellImage:D,hrefMainTableId:v,registerOnlineHrefModal:T,registerPopModal:I,openPopModal:x,openOnlineHrefModal:O,onlinePopModalRef:y,popTableId:L,handleClickFieldHref:B}}const kn=Rt({name:"OnlinePopListModal",props:{id:{type:String,default:""},multi:{type:Boolean,default:!1},addAuth:{type:Boolean,default:!0}},components:{BasicModal:Bt,BasicTable:zi,TableAction:Ai,PlusOutlined:rt,OnlinePopModal:Ue},emits:["success","register"],setup(i,{emit:e}){const{createMessage:t}=Pe(),{popModalFixedWidth:l,resetBodyStyle:r,popBodyStyle:s}=Gt(),u=k(""),w=k(800),[f,{closeModal:T}]=Dt(Q=>{u.value="",p.value=Q.selectedRowKeys,S.value=Q.selectedRows,n({current:1}),h(),r()}),[O,{openModal:v}]=Oe();function I(){T()}const x=pe(()=>{const Q=p.value;return!(Q&&Q.length>0)}),L=k(!1);function _(){L.value=!0;let Q=be(S.value);Q&&Q.length>0&&(e("success",Q),T()),setTimeout(()=>{L.value=!1},200)}function Y(Q){const q="/online/cgform/api/getData/"+i.id;return me.get({url:q,params:Q})}function J(Q){return Q.column="id",new Promise((q,ee)=>W(this,null,function*(){const te=yield Y(Q);q(te)}))}const B={isPopList:!0,reloadTable(){},isTree(){return!1}},j=k({}),{columns:V,downloadRowFile:z,getImgView:K,getPcaText:G,getFormatDate:F,handleColumnResult:A,hrefComponent:b,viewOnlineCellImage:P}=Tn(B,j);function D(){const Q="/online/cgform/api/getColumns/"+i.id;return new Promise((q,ee)=>{me.get({url:Q},{isTransformResponse:!1}).then(te=>{te.success?q(te.result):(t.warning(te.message),ee())})})}const y=k("");ge(()=>i.id,()=>W(this,null,function*(){let Q=yield D();A(Q),y.value=Q.description}),{immediate:!0});const{tableContext:a}=Yi({designScope:"process-design",pagination:!0,tableProps:{title:"",api:J,clickToRowSelect:!0,columns:V,showTableSetting:!1,immediate:!1,canResize:!1,showActionColumn:!1,actionColumn:{dataIndex:"action",slots:{customRender:"action"}},useSearchForm:!1,beforeFetch:Q=>H(Q)}}),[o,{reload:h,setPagination:n},{rowSelection:c,selectedRowKeys:p,selectedRows:S}]=a;ge(()=>i.multi,Q=>{Q==!0?c.type="checkbox":c.type="radio"},{immediate:!0});function C(Q){return[{label:"编辑",onClick:g.bind(null,Q)}]}function g(Q){}function E(){h()}const M=["int","double","Date","Datetime","BigDecimal"];function H(Q){let q=u.value;if(!q)return Q.superQueryMatchType="or",Q.superQueryParams="",Q;let ee=V.value,te=[];if(ee&&ee.length>0)for(let le of ee)le.dbType&&(le.dbType=="string"?te.push({field:le.dataIndex,type:le.dbType.toLowerCase(),rule:"like",val:q}):le.dbType=="Date"?q.length==10&&te.push({field:le.dataIndex,type:le.dbType.toLowerCase(),rule:"eq",val:q}):le.dbType=="Datetime"?q.length==19&&te.push({field:le.dataIndex,type:le.dbType.toLowerCase(),rule:"eq",val:q}):M.indexOf(le.dbType)&&te.push({field:le.dataIndex,type:le.dbType.toLowerCase(),rule:"eq",val:q}));return Q.superQueryMatchType="or",Q.superQueryParams=encodeURI(JSON.stringify(te)),Q}function N(){v(!0,{})}function se(Q){e("success",[Q]),T()}return{registerModal:f,modalWidth:w,handleCancel:I,submitDisabled:x,submitLoading:L,handleSubmit:_,registerTable:o,getTableAction:C,searchText:u,onSearch:E,downloadRowFile:z,getImgView:K,getPcaText:G,getFormatDate:F,hrefComponent:b,viewOnlineCellImage:P,rowSelection:c,modalTitle:y,registerPopModal:O,handleAdd:N,reload:h,popModalFixedWidth:l,popBodyStyle:s,handleDataSave:se}}}),In={style:{display:"inline-block",width:"calc(100% - 140px)","text-align":"left"}},On={key:0,style:{"font-size":"12px","font-style":"italic"}},Dn={key:0,style:{"font-size":"12px","font-style":"italic"}},Pn=["src","onClick"],Rn=["innerHTML"],Mn=["title"];function En(i,e,t,l,r,s){const u=ie("PlusOutlined"),w=ie("a-button"),f=ie("a-input-search"),T=ie("TableAction"),O=ie("BasicTable"),v=ie("BasicModal"),I=ie("online-pop-modal");return X(),de(Ve,null,[ce(v,{onRegister:i.registerModal,width:i.popModalFixedWidth,dialogStyle:{top:"70px"},bodyStyle:i.popBodyStyle,title:i.modalTitle,wrapClassName:"jeecg-online-pop-list-modal"},{footer:ae(()=>[fe("div",In,[i.addAuth?(X(),ke(w,{key:0,style:{"border-radius":"50px"},type:"primary",onClick:i.handleAdd},{default:ae(()=>[ce(u),Fe("新增记录")]),_:1},8,["onClick"])):Se("",!0)]),ce(w,{key:"back",onClick:i.handleCancel},{default:ae(()=>[Fe("关闭")]),_:1},8,["onClick"]),ce(w,{disabled:i.submitDisabled,key:"submit",type:"primary",onClick:i.handleSubmit,loading:i.submitLoading},{default:ae(()=>[Fe("确定")]),_:1},8,["disabled","onClick","loading"])]),default:ae(()=>[ce(O,{onRegister:i.registerTable,rowSelection:i.rowSelection},{tableTitle:ae(()=>[ce(f,{value:i.searchText,"onUpdate:value":e[0]||(e[0]=x=>i.searchText=x),onSearch:i.onSearch,placeholder:"请输入关键词，按回车搜索",style:{width:"240px"}},null,8,["value","onSearch"])]),action:ae(({record:x})=>[ce(T,{actions:i.getTableAction(x)},null,8,["actions"])]),fileSlot:ae(({text:x})=>[x?(X(),ke(w,{key:1,ghost:!0,type:"primary",preIcon:"ant-design:download",size:"small",onClick:L=>i.downloadRowFile(x)},{default:ae(()=>[Fe(" 下载 ")]),_:2},1032,["onClick"])):(X(),de("span",On,"无文件"))]),imgSlot:ae(({text:x})=>[x?(X(),de("img",{key:1,src:i.getImgView(x),alt:"图片不存在",class:"online-cell-image",onClick:L=>i.viewOnlineCellImage(x)},null,8,Pn)):(X(),de("span",Dn,"无图片"))]),htmlSlot:ae(({text:x})=>[fe("div",{innerHTML:x},null,8,Rn)]),pcaSlot:ae(({text:x})=>[fe("div",{title:i.getPcaText(x)},Te(i.getPcaText(x)),9,Mn)]),dateSlot:ae(({text:x,column:L})=>[fe("span",null,Te(i.getFormatDate(x,L)),1)]),_:1},8,["onRegister","rowSelection"])]),_:1},8,["onRegister","width","bodyStyle","title"]),ce(I,{id:i.id,onRegister:i.registerPopModal,onSuccess:i.handleDataSave,topTip:""},null,8,["id","onRegister","onSuccess"])],64)}const ei=Me(kn,[["render",En]]),ds=Object.freeze(Object.defineProperty({__proto__:null,default:ei},Symbol.toStringTag,{value:"Module"})),Bn={name:"LinkTableCard",props:{valueField:oe.string.def(""),textField:oe.string.def(""),tableName:oe.string.def(""),multi:oe.bool.def(!1),value:oe.oneOfType([oe.string,oe.number]),linkFields:oe.array.def([]),disabled:oe.bool.def(!1),detail:oe.bool.def(!1),imageField:oe.string.def("")},components:{PlusOutlined:rt,MinusCircleFilled:Vi,OnlinePopListModal:ei,OnlinePopModal:Ue},emits:["change","update:value"],setup(i,{emit:e}){const t=pe(()=>i.tableName),[l,{openModal:r}]=Oe(),[s,{openModal:u}]=Oe(),w=k([]),f=k([]),T=k(null),O=k(0),v=pe(()=>!(i.disabled==!0||i.multi===!1&&f.value.length>0)),{auths:I,otherColumns:x,realShowColumns:L,tableColumns:_,textFieldArray:Y,transData:J,loadOne:B,compareData:j,formatData:V,initFormData:z,getImageSrc:K,showImage:G}=Zt(i),F=pe(()=>i.multi===!0?12:24),A=pe(()=>i.multi===!0?24:12);function b(c){if(c&&Y.value.length>0){let p=Y.value[0];return c[p]}}function P(c){c==null||c.stopPropagation(),c==null||c.preventDefault()}function D(c,p){P(c),I.update!=!1&&i.disabled==!1&&u(!0,{isUpdate:!0,record:p})}function y(c){r(!0,{selectedRowKeys:f.value.map(p=>p.id),selectedRows:[...f.value]})}function a(c){let p=[];for(let S of c){let C=he({},S);J(C),p.push(C)}f.value=p,n()}function o(c){let p=f.value;for(let S=0;S<p.length;S++)if(p[S].id===c.id){let C=he({},c);J(C),p.splice(S,1,C)}f.value=p,n()}function h(c,p){P(c);let S=f.value;S&&S.length>p&&(S.splice(p,1),f.value=S),n()}function n(){let c=f.value,p=[],S={},C=i.linkFields;if(c.length>0)for(let E=0;E<c.length;E++)p.push(c[E][i.valueField]),z(S,C,c[E]);else z(S,C);let g=p.join(",");V(S),e("change",g,S),e("update:value",g)}return ge(()=>i.value,c=>W(this,null,function*(){if(c){if(j(f.value,c)===!1){let p=yield B(c);f.value=p}i.linkFields&&i.linkFields.length>0&&n()}else f.value=[]}),{immediate:!0}),Et(()=>{T.value.offsetWidth<250&&(O.value=24)}),{popTableName:t,selectRecords:f,otherColumns:x,realShowColumns:L,showButton:v,selectValue:w,handleAddRecord:y,handleDeleteRecord:h,getMainContent:b,itemSpan:F,columnSpan:A,tableColumns:_,addCard:a,registerListModal:l,registerFormModal:s,handleClickEdit:D,updateCardData:o,getImageSrc:K,showImage:G,auths:I,tableLinkCardRef:T,fixedSpan:O,placeholderImage:Ye,handleImageError:c=>{c.target.src=Ye}}}},jn={ref:"tableLinkCardRef"},_n={class:"table-link-card"},Qn={style:{width:"100%",height:"100%"}},Jn={key:0,class:"card-button"},Nn=["onClick"],Ln={key:0,class:"card-delete"},$n={class:"card-inner"},Vn={class:"card-main-content"},Hn={class:"other-content"},Yn={class:"label ellipsis"},Un={class:"text ellipsis"},qn={key:0,class:"card-item-image"},Kn=["src"];function Wn(i,e,t,l,r,s){const u=ie("PlusOutlined"),w=ie("a-button"),f=ie("minus-circle-filled"),T=ie("a-col"),O=ie("a-row"),v=ie("online-pop-list-modal"),I=ie("online-pop-modal");return X(),de("div",jn,[fe("div",_n,[fe("div",Qn,[l.showButton?(X(),de("div",Jn,[ce(w,{onClick:l.handleAddRecord},{default:ae(()=>[ce(u),Fe("记 录")]),_:1},8,["onClick"])])):Se("",!0),ce(O,null,{default:ae(()=>[(X(!0),de(Ve,null,lt(l.selectRecords,(x,L)=>(X(),ke(T,{span:l.fixedSpan?l.fixedSpan:l.itemSpan},{default:ae(()=>[fe("div",{class:$e(["card-item",{"disabled-chunk":t.detail==!0}]),onClick:_=>l.handleClickEdit(_,x)},[fe("div",{class:$e(["card-item-left",{"show-right-image":l.getImageSrc(x)}])},[t.disabled==!1?(X(),de("span",Ln,[ce(f,{onClick:_=>l.handleDeleteRecord(_,L)},null,8,["onClick"])])):Se("",!0),fe("div",$n,[fe("div",Vn,Te(l.getMainContent(x)),1),fe("div",Hn,[ce(O,null,{default:ae(()=>[(X(!0),de(Ve,null,lt(l.realShowColumns,_=>(X(),ke(T,{span:l.columnSpan},{default:ae(()=>[fe("span",Yn,Te(_.title),1),fe("span",Un,Te(x[_.dataIndex]),1)]),_:2},1032,["span"]))),256))]),_:2},1024)])])],2),l.getImageSrc(x)?(X(),de("div",qn,[l.getImageSrc(x)?(X(),de("img",{key:0,src:l.getImageSrc(x),alt:"",onError:e[0]||(e[0]=(..._)=>l.handleImageError&&l.handleImageError(..._))},null,40,Kn)):Se("",!0)])):Se("",!0)],10,Nn)]),_:2},1032,["span"]))),256))]),_:1})])]),ce(v,{onRegister:l.registerListModal,multi:t.multi,id:l.popTableName,addAuth:l.auths.add,onSuccess:l.addCard},null,8,["onRegister","multi","id","addAuth","onSuccess"]),ce(I,{id:l.popTableName,onRegister:l.registerFormModal,onSuccess:l.updateCardData,topTip:""},null,8,["id","onRegister","onSuccess"])],512)}const ti=Me(Bn,[["render",Wn],["__scopeId","data-v-6c31f866"]]),cs=Object.freeze(Object.defineProperty({__proto__:null,default:ti},Symbol.toStringTag,{value:"Module"})),St={};function zn(){i("OnlineSelectCascade",qi),i("LinkTableSelect",Xt),i("LinkTableCard",ti);function i(t,l){St[t]||(Ci(t,l),St[t]=1)}function e(t){t.component=="LinkTableCard"&&(t.component="LinkTableSelect",t.componentProps.popContainer="body")}return{addComponent:i,linkTableCard2Select:e}}export{is as E,Xe as F,ti as L,Ue as O,il as S,Be as V,Tn as a,pn as b,Wt as c,Xl as d,qt as e,es as f,Re as g,ll as h,zn as i,ve as j,os as k,st as l,ls as m,ns as n,pt as o,en as p,xt as q,ts as r,ss as s,as as t,on as u,rs as v,us as w,ds as x,cs as y};
