import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { rules } from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';

// 列表页表格列定义
export const columns: BasicColumn[] = [
  {
    title: '保单号',
    dataIndex: 'policyNo',
    width: 150,
  },
  {
    title: '保单生效日期',
    dataIndex: 'calculationDate',
    width: 120,
    customRender: ({ text }) => {
      return render.renderDate(text);
    },
  },
  {
    title: '产品英文名称',
    dataIndex: 'planName',
    width: 200,
  },
  {
    title: '佣金期数',
    dataIndex: 'yearNum',
    width: 100,
  },
  {
    title: '保费金额',
    dataIndex: 'premiumAmount',
    width: 120,
    customRender: ({ text }) => {
      return render.renderMoney(text);
    },
  },
  {
    title: '佣金金额',
    dataIndex: 'commissionAmount',
    width: 120,
    customRender: ({ text }) => {
      return render.renderMoney(text);
    },
  },
  {
    title: '缴费币种',
    dataIndex: 'paymentCurrency',
    width: 100,
    customRender: ({ text }) => {
      return render.renderDict(text, 'payment_currency');
    },
  },
  {
    title: '合约手续费率',
    dataIndex: 'contractRate',
    width: 120,
    customRender: ({ text }) => {
      return text ? (text).toFixed(2) + '%' : '';
    },
  },
  {
    title: '销售员佣金率',
    dataIndex: 'commissionRate',
    width: 120,
    customRender: ({ text }) => {
      return text ? (text).toFixed(2) + '%' : '';
    },
  },
  {
    title: '佣金结算日汇率',
    dataIndex: 'rcExchangeRate',
    width: 120,
    customRender: ({ text }) => {
      return text ? text.toFixed(4) : '';
    },
  },
];

// 搜索表单定义
export const searchFormSchema: FormSchema[] = [
  {
    field: 'policyNo',
    label: '保单号',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    field: 'companyCode',
    label: '公司',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'supplier',
      placeholder: '请选择公司',
    },
    colProps: { span: 6 },
  },
  {
    field: 'yearNum',
    label: '佣金期数',
    component: 'InputNumber',
    componentProps: {
      placeholder: '请输入佣金期数',
      min: 1,
    },
    colProps: { span: 6 },
  },
  {
    field: 'calculationDateRange',
    label: '保单生效日期',
    component: 'RangePicker',
    componentProps: {
      format: 'YYYY-MM-DD',
      placeholder: ['开始日期', '结束日期'],
    },
    colProps: { span: 6 },
  },
];

// 表单字段定义
export const formSchema: FormSchema[] = [
  {
    label: '',
    field: 'id',
    component: 'Input',
    show: false,
  },
  {
    label: '保单号',
    field: 'policyNo',
    component: 'Input',
    required: true,
  },
  {
    label: '计划名称',
    field: 'planName',
    component: 'Input',
  },
  {
    label: '公司编码',
    field: 'companyCode',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'supplier',
    },
  },
  {
    label: '佣金期数',
    field: 'yearNum',
    component: 'InputNumber',
    componentProps: {
      min: 1,
    },
  },
  {
    label: '保费金额',
    field: 'premiumAmount',
    component: 'InputNumber',
    componentProps: {
      precision: 2,
      min: 0,
    },
  },
  {
    label: '佣金率',
    field: 'commissionRate',
    component: 'InputNumber',
    componentProps: {
      precision: 4,
      min: 0,
      max: 1,
    },
  },
  {
    label: '佣金金额',
    field: 'commissionAmount',
    component: 'InputNumber',
    componentProps: {
      precision: 2,
      min: 0,
    },
  },
  {
    label: '缴费币种',
    field: 'paymentCurrency',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'payment_currency',
    },
  },
  {
    label: '计算日期',
    field: 'calculationDate',
    component: 'DatePicker',
    componentProps: {
      format: 'YYYY-MM-DD',
    },
  },
  {
    label: '佣金结算日汇率',
    field: 'rcExchangeRate',
    component: 'InputNumber',
    componentProps: {
      precision: 4,
      min: 0,
    },
  },
];
