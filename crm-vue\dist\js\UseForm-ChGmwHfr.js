import{d as P,ag as i,aB as b,ar as F,aD as e,at as m,k as s,G as u}from"./vue-vendor-CnsIXr4W.js";import"./index-D4YmpXsT.js";import{C as E}from"./index-BNka1IZr.js";import{u as v,_ as k}from"./index-BnxvmHFv.js";import{P as B}from"./index-DF4dbr8b.js";import{B as D,u as $}from"./useForm-DNLebgae.js";import"./componentMap-Bg13cs1o.js";import"./antd-vue-vendor-ac69AFxs.js";import"./useFormItem-Eoh0MXq5.js";import"./index-j6u-UpGU.js";import"./BasicModal-B-mPgnfO.js";import"./useTimeout-DoPujGLn.js";import"./vxe-table-vendor-CK0mysZD.js";import"./useWindowSizeFn-BcaCTZPc.js";import"./download-ZmOT1Dx2.js";import"./base64Conver-24EVOS6V.js";import"./index-DBQnQ9LG.js";import"./index-CUrp7h3O.js";import"./useCountdown-D_p0DKZc.js";import"./useFormItemSingle-D8EqzImO.js";import"./JSelectUser-Blw_A8N4.js";import"./props-DHLM7ils.js";import"./JSelectBiz-rbVGd4Dv.js";import"./JAddInput-B7aUDeJi.js";import"./areaDataUtil-ChdaTOUz.js";import"./index-86WEzeSw.js";import"./index-Dr3sBsWR.js";import"./bem-DfjdT9o5.js";import"./props-WP5sspMu.js";import"./useContextMenu-DwGGVBbH.js";import"./depart.api-BTFSMQ51.js";import"./JSelectDept-1TdcISOX.js";import"./JPopup-gJ1mXmQZ.js";import"./JEllipsis-IqMnhApY.js";import"./JUpload-DCci6z5o.js";import"./index-C3YRp4LI.js";import"./index-mK2t00gc.js";import"./JAreaLinkage-BDz8pfno.js";import"./JCodeEditor-C8R0_wo6.js";import"./htmlmixed-XbbCJteX.js";import"./vue-BLlfMlNv.js";/* empty css             */import"./EasyCronInput-BB8IcbzA.js";import"./index-DmtYkh1u.js";import"./index-B4xK7O2u.js";import"./useIntersectionObserver-DZYoTRf_.js";import"./useContentHeight-iTASQkhf.js";import"./useContentViewHeight-CKAvo1h-.js";import"./usePageContext-AIHLEzkU.js";import"./injectionKey-DPVn4AgL.js";const a=[{field:"field1",component:"Input",label:"字段1",colProps:{span:8},componentProps:{placeholder:"自定义placeholder",onChange:t=>{}}},{field:"field2",component:"Input",label:"字段2",colProps:{span:8}},{field:"field3",component:"DatePicker",label:"字段3",colProps:{span:8}},{field:"fieldTime",component:"RangePicker",label:"时间字段",defaultValue:[new Date("2024-03-21"),new Date("2024-03-27")],componentProps:{valueType:"Date"},colProps:{span:8}},{field:"field4",component:"Select",label:"字段4",colProps:{span:8},componentProps:{options:[{label:"选项1",value:"1",key:"1"},{label:"选项2",value:"2",key:"2"}]}},{field:"field5",component:"CheckboxGroup",label:"字段5",colProps:{span:8},componentProps:{options:[{label:"选项1",value:"1"},{label:"选项2",value:"2"}]}},{field:"field7",component:"RadioGroup",label:"字段7",colProps:{span:8},componentProps:{options:[{label:"选项1",value:"1"},{label:"选项2",value:"2"}]}}],A=P({components:{BasicForm:D,CollapseContainer:E,PageWrapper:B},setup(){const{createMessage:t}=v(),[o,{setProps:n}]=$({labelWidth:120,schemas:a,actionColOptions:{span:24},fieldMapToTime:[["fieldTime",["startTime","endTime"],"YYYY-MM"]]});return{register:o,schemas:a,handleSubmit:p=>{t.success("click search,values:"+JSON.stringify(p))},setProps:n}}}),g={class:"mb-4"},w={class:"mb-4"};function S(t,o,n,p,T,W){const r=i("a-button"),d=i("BasicForm"),f=i("CollapseContainer"),C=i("PageWrapper");return F(),b(C,{title:"UseForm操作示例"},{default:e(()=>[m("div",g,[s(r,{onClick:o[0]||(o[0]=l=>t.setProps({labelWidth:150})),class:"mr-2"},{default:e(()=>o[17]||(o[17]=[u(" 更改labelWidth ")])),_:1}),s(r,{onClick:o[1]||(o[1]=l=>t.setProps({labelWidth:120})),class:"mr-2"},{default:e(()=>o[18]||(o[18]=[u(" 还原labelWidth ")])),_:1}),s(r,{onClick:o[2]||(o[2]=l=>t.setProps({size:"large"})),class:"mr-2"},{default:e(()=>o[19]||(o[19]=[u(" 更改Size ")])),_:1}),s(r,{onClick:o[3]||(o[3]=l=>t.setProps({size:"default"})),class:"mr-2"},{default:e(()=>o[20]||(o[20]=[u(" 还原Size ")])),_:1}),s(r,{onClick:o[4]||(o[4]=l=>t.setProps({disabled:!0})),class:"mr-2"},{default:e(()=>o[21]||(o[21]=[u(" 禁用表单 ")])),_:1}),s(r,{onClick:o[5]||(o[5]=l=>t.setProps({disabled:!1})),class:"mr-2"},{default:e(()=>o[22]||(o[22]=[u(" 解除禁用 ")])),_:1}),s(r,{onClick:o[6]||(o[6]=l=>t.setProps({compact:!0})),class:"mr-2"},{default:e(()=>o[23]||(o[23]=[u(" 紧凑表单 ")])),_:1}),s(r,{onClick:o[7]||(o[7]=l=>t.setProps({compact:!1})),class:"mr-2"},{default:e(()=>o[24]||(o[24]=[u(" 还原正常间距 ")])),_:1}),s(r,{onClick:o[8]||(o[8]=l=>t.setProps({actionColOptions:{span:8}})),class:"mr-2"},{default:e(()=>o[25]||(o[25]=[u(" 操作按钮位置 ")])),_:1})]),m("div",w,[s(r,{onClick:o[9]||(o[9]=l=>t.setProps({showActionButtonGroup:!1})),class:"mr-2"},{default:e(()=>o[26]||(o[26]=[u(" 隐藏操作按钮 ")])),_:1}),s(r,{onClick:o[10]||(o[10]=l=>t.setProps({showActionButtonGroup:!0})),class:"mr-2"},{default:e(()=>o[27]||(o[27]=[u(" 显示操作按钮 ")])),_:1}),s(r,{onClick:o[11]||(o[11]=l=>t.setProps({showResetButton:!1})),class:"mr-2"},{default:e(()=>o[28]||(o[28]=[u(" 隐藏重置按钮 ")])),_:1}),s(r,{onClick:o[12]||(o[12]=l=>t.setProps({showResetButton:!0})),class:"mr-2"},{default:e(()=>o[29]||(o[29]=[u(" 显示重置按钮 ")])),_:1}),s(r,{onClick:o[13]||(o[13]=l=>t.setProps({showSubmitButton:!1})),class:"mr-2"},{default:e(()=>o[30]||(o[30]=[u(" 隐藏查询按钮 ")])),_:1}),s(r,{onClick:o[14]||(o[14]=l=>t.setProps({showSubmitButton:!0})),class:"mr-2"},{default:e(()=>o[31]||(o[31]=[u(" 显示查询按钮 ")])),_:1}),s(r,{onClick:o[15]||(o[15]=l=>t.setProps({resetButtonOptions:{disabled:!0,text:"重置New"}})),class:"mr-2"},{default:e(()=>o[32]||(o[32]=[u(" 修改重置按钮 ")])),_:1}),s(r,{onClick:o[16]||(o[16]=l=>t.setProps({submitButtonOptions:{disabled:!0,loading:!0}})),class:"mr-2"},{default:e(()=>o[33]||(o[33]=[u(" 修改查询按钮 ")])),_:1})]),s(f,{title:"useForm示例"},{default:e(()=>[s(d,{onRegister:t.register,onSubmit:t.handleSubmit},null,8,["onRegister","onSubmit"])]),_:1})]),_:1})}const So=k(A,[["render",S]]);export{So as default};
