import{e as t,u as e}from"./vue-vendor-CnsIXr4W.js";import{y as O,z as P,A as k,B as y,M as l}from"./index-BnxvmHFv.js";import"./antd-vue-vendor-ac69AFxs.js";import"./vxe-table-vendor-CK0mysZD.js";function z(){const{getFullContent:S}=O(),o=P(),a=t(()=>!e(S)&&e(s)&&e(r)&&!e(u)&&!e(g)),p=t(()=>!e(H)&&!e(a)),m=t(()=>{const n=!e(S)&&e(r);return n&&!e(s)||n&&e(u)||n&&e(g)}),{getMenuMode:c,getSplit:d,getShowHeaderTrigger:f,getIsSidebarType:i,getIsMixSidebar:g,getIsTopMenu:u}=k(),{getShowBreadCrumb:h,getShowLogo:M}=y(),s=t(()=>!e(i)&&e(r)),F=t(()=>o.getHeaderSetting.showDoc),C=t(()=>o.getHeaderSetting.theme),r=t(()=>o.getHeaderSetting.show),H=t(()=>o.getHeaderSetting.fixed),T=t(()=>o.getHeaderSetting.bgColor),x=t(()=>o.getHeaderSetting.showSearch),I=t(()=>o.getHeaderSetting.useLockPage),L=t(()=>o.getHeaderSetting.showFullScreen),R=t(()=>o.getHeaderSetting.showNotice),w=t(()=>e(c)!==l.HORIZONTAL&&e(h)&&!e(d)),A=t(()=>e(c)!==l.HORIZONTAL&&!e(h)&&!e(d)),B=t(()=>e(M)&&!e(i)&&!e(g)),b=t(()=>e(w)||e(f));function N(n){o.setProjectConfig({headerSetting:n})}return{setHeaderSetting:N,getShowDoc:F,getShowSearch:x,getHeaderTheme:C,getUseLockPage:I,getShowFullScreen:L,getShowNotice:R,getShowBread:w,getShowContent:b,getShowHeaderLogo:B,getShowHeader:r,getFixed:H,getShowMixHeaderRef:s,getShowFullHeaderRef:a,getShowInsetHeaderRef:m,getUnFixedAndFull:p,getHeaderBgColor:T,getShowBreadTitle:A}}export{z as useHeaderSetting};
