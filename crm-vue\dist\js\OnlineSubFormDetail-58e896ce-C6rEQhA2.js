import{cl as b,u as F,e as S,bl as y}from"./index-BnxvmHFv.js";import{f,w as d,ag as w,aB as B,ar as k}from"./vue-vendor-CnsIXr4W.js";import"./index-D4YmpXsT.js";import{k as D,g as T}from"./useExtendComponent-dcae5c78-C--8iMn7.js";import x from"./DetailForm-deecbf3d-D32RlJyb.js";import"./componentMap-Bg13cs1o.js";import"./antd-vue-vendor-ac69AFxs.js";import"./index-j6u-UpGU.js";import"./constant-fa63bd66-Ddbq-fz2.js";import"./index-C9Hr1YQz.js";import"./user.api-_jc-Tnyz.js";import"./customExpression-CtXxD800.js";import"./index-DjTWLsmR.js";import"./useListPage-BCVdKj1d.js";import"./LinkTableListPiece-f3a8e0d7-BR0-U9EN.js";import"./OnlineSelectCascade-05c40fef-PZGR6IPg.js";import"./JModalTip-b055ab60-Co9Wp31o.js";import"./index-_aSFLs8i.js";import{B as I}from"./useForm-DNLebgae.js";import"./vxe-table-vendor-CK0mysZD.js";import"./JAreaLinkage-BDz8pfno.js";import"./areaDataUtil-ChdaTOUz.js";import"./JSelectUser-Blw_A8N4.js";import"./props-DHLM7ils.js";import"./JSelectBiz-rbVGd4Dv.js";import"./JSelectDept-1TdcISOX.js";import"./index-Dr3sBsWR.js";import"./index-BNka1IZr.js";import"./index-DmtYkh1u.js";import"./index-B4xK7O2u.js";import"./useTimeout-DoPujGLn.js";import"./useIntersectionObserver-DZYoTRf_.js";import"./bem-DfjdT9o5.js";import"./props-WP5sspMu.js";import"./useContextMenu-DwGGVBbH.js";import"./JCodeEditor-C8R0_wo6.js";import"./useFormItem-Eoh0MXq5.js";import"./htmlmixed-XbbCJteX.js";import"./vue-BLlfMlNv.js";/* empty css             */import"./JAddInput-B7aUDeJi.js";import"./index-86WEzeSw.js";import"./depart.api-BTFSMQ51.js";import"./JPopup-gJ1mXmQZ.js";import"./EasyCronInput-BB8IcbzA.js";import"./JEllipsis-IqMnhApY.js";import"./JUpload-DCci6z5o.js";import"./BasicTable-DO8Vk_Gm.js";import"./injectionKey-DPVn4AgL.js";import"./useWindowSizeFn-BcaCTZPc.js";import"./BasicModal-B-mPgnfO.js";import"./download-ZmOT1Dx2.js";import"./base64Conver-24EVOS6V.js";import"./index-DBQnQ9LG.js";import"./index-CUrp7h3O.js";import"./useCountdown-D_p0DKZc.js";import"./useFormItemSingle-D8EqzImO.js";import"./index-C3YRp4LI.js";import"./index-mK2t00gc.js";var O=(t,i,o)=>new Promise((m,e)=>{var l=r=>{try{a(o.next(r))}catch(s){e(s)}},p=r=>{try{a(o.throw(r))}catch(s){e(s)}},a=r=>r.done?m(r.value):Promise.resolve(r.value).then(l,p);a((o=o.apply(t,i)).next())});const P="/online/cgform/api/subform",$={name:"OnlineSubFormDetail",components:{BasicForm:I,Loading:y,DetailForm:x},props:{properties:{type:Object,required:!0},mainId:{type:String,default:""},table:{type:String,default:""},formTemplate:{type:Number,default:1}},emits:["formChange"],setup(t){const i=f(!1),{createMessage:o}=F(),m=f(""),e=f({}),{detailFormSchemas:l,createFormSchemas:p,formSpan:a}=D(t);d(()=>t.table,()=>{m.value=t.table},{immediate:!0}),d(()=>t.properties,()=>{i.value=!1,p(t.properties),i.value=!0},{deep:!0,immediate:!0}),d(()=>t.mainId,()=>{setTimeout(()=>{r()},100)},{immediate:!0});function r(){return O(this,null,function*(){yield T(i),e.value={};const{table:n,mainId:c}=t;if(!n||!c)return;let u=(yield s(n,c))||{};e.value=u})}function s(n,c){let u=`${P}/${n}/${c}`;return new Promise((g,v)=>{S.get({url:u},{isTransformResponse:!1}).then(h=>{h.success?g(h.result):v()})}).catch(()=>{o.warning("子表获取数据失败:"+n)})}return{detailFormSchemas:l,subFormData:e,formSpan:a}}};function j(t,i,o,m,e,l){const p=w("detail-form");return k(),B(p,{schemas:m.detailFormSchemas,data:m.subFormData,span:m.formSpan},null,8,["schemas","data","span"])}const Lt=b($,[["render",j]]);export{Lt as default};
