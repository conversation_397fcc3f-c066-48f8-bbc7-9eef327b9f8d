<template>
  <div class="p-4">
    <h2>佣金计算查询测试页面</h2>
    
    <a-card title="API测试" class="mb-4">
      <a-space direction="vertical" style="width: 100%">
        <a-row :gutter="16">
          <a-col :span="6">
            <a-input v-model:value="testParams.policyNo" placeholder="保单号" />
          </a-col>
          <a-col :span="6">
            <a-select v-model:value="testParams.companyCode" placeholder="选择公司" style="width: 100%">
              <a-select-option value="AIA">AIA</a-select-option>
              <a-select-option value="PRUDENTIAL">PRUDENTIAL</a-select-option>
            </a-select>
          </a-col>
          <a-col :span="6">
            <a-input-number v-model:value="testParams.yearNum" placeholder="佣金期数" style="width: 100%" />
          </a-col>
          <a-col :span="6">
            <a-button type="primary" @click="testQuery">测试查询</a-button>
          </a-col>
        </a-row>
        
        <a-row :gutter="16">
          <a-col :span="12">
            <a-date-picker v-model:value="testParams.calculationDateStart" placeholder="开始日期" style="width: 100%" />
          </a-col>
          <a-col :span="12">
            <a-date-picker v-model:value="testParams.calculationDateEnd" placeholder="结束日期" style="width: 100%" />
          </a-col>
        </a-row>
      </a-space>
    </a-card>

    <a-card title="查询结果" v-if="queryResult">
      <pre>{{ JSON.stringify(queryResult, null, 2) }}</pre>
    </a-card>

    <a-card title="错误信息" v-if="errorMessage">
      <a-alert :message="errorMessage" type="error" />
    </a-card>
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive } from 'vue';
  import { queryCommissionInfo } from './CrmCommissionCalculation.api';
  import { message } from 'ant-design-vue';

  const testParams = reactive({
    policyNo: '',
    companyCode: '',
    yearNum: null,
    calculationDateStart: null,
    calculationDateEnd: null,
    pageNo: 1,
    pageSize: 10,
  });

  const queryResult = ref(null);
  const errorMessage = ref('');

  async function testQuery() {
    try {
      errorMessage.value = '';
      queryResult.value = null;
      
      const params = {
        ...testParams,
        calculationDateStart: testParams.calculationDateStart ? testParams.calculationDateStart.format('YYYY-MM-DD') : null,
        calculationDateEnd: testParams.calculationDateEnd ? testParams.calculationDateEnd.format('YYYY-MM-DD') : null,
      };
      
      console.log('查询参数:', params);
      
      const result = await queryCommissionInfo(params);
      queryResult.value = result;
      
      message.success('查询成功');
    } catch (error) {
      console.error('查询失败:', error);
      errorMessage.value = error.message || '查询失败';
      message.error('查询失败');
    }
  }
</script>

<style scoped>
  .mb-4 {
    margin-bottom: 16px;
  }
</style>
