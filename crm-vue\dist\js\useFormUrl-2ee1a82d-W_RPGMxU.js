import{aN as c,f as i,u as f}from"./vue-vendor-CnsIXr4W.js";import{ac as l}from"./index-BnxvmHFv.js";import{f as k}from"./antd-vue-vendor-ac69AFxs.js";function T(){const n=c(),e=i(n.query.token),o=i(""),t=l();f(e)||(t.getToken?e.value=t.getToken:u());function u(){const s=k.loading("获取token中...",0);setTimeout(()=>{o.value="eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3MTAwMTc2NzUsInVzZXJuYW1lIjoiYWRtaW4ifQ.0q5ywkLF144SaqumsSgEXO5ERtqaYp8jqouIUxxhELc",a(),r(),s()},3e3)}function a(){t.setToken(o.value)}function r(){window.location.replace(`${n.fullPath}?token=${o.value}`)}return{token:e}}export{T as g};
