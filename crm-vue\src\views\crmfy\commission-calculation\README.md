# 佣金计算查询功能

## 功能描述

实现分页查询订单佣金信息和导出Excel功能，用于查询保险公司账单列表。

## 功能特性

### 查询条件
- **保单号**: 支持模糊查询
- **公司**: 下拉选择保险公司
- **佣金期数**: 输入具体期数
- **保单生效日期区间**: 选择日期范围

### 输出字段
- 保单号
- 保单生效日期(calculation_date)
- 产品英文名称
- 佣金期数(year_num)
- 保费金额
- 佣金金额
- 缴费币种
- 合约手续费率
- 销售员佣金率
- 佣金结算日当天汇率

## API接口

### 1. 分页查询佣金计算信息
```
GET /crmcommissioncalculation/crmCommissionCalculation/queryCommissionInfo
```

**请求参数:**
- `pageNo`: 页码 (默认: 1)
- `pageSize`: 每页大小 (默认: 10)
- `policyNo`: 保单号 (可选)
- `companyCode`: 公司编码 (可选)
- `yearNum`: 佣金期数 (可选)
- `calculationDateStart`: 保单生效日期开始 (可选)
- `calculationDateEnd`: 保单生效日期结束 (可选)

**响应示例:**
```json
{
  "success": true,
  "result": {
    "records": [
      {
        "policyNo": "POL001",
        "calculationDate": "2024-01-01",
        "planName": "Medical Plan A",
        "yearNum": 1,
        "premiumAmount": 10000.00,
        "commissionAmount": 1000.00,
        "paymentCurrency": "HKD",
        "contractRate": 0.15,
        "commissionRate": 0.10,
        "rcExchangeRate": 7.8000
      }
    ],
    "total": 100,
    "size": 10,
    "current": 1
  }
}
```

### 2. 导出Excel
```
GET /crmcommissioncalculation/crmCommissionCalculation/exportCommissionInfoXls
```

**请求参数:** 同查询接口

**响应:** Excel文件下载

## 前端组件

### 文件结构
```
commission-calculation/
├── CrmCommissionCalculation.api.ts     # API接口定义
├── CrmCommissionCalculation.data.ts    # 表格列和表单配置
├── CrmCommissionCalculationList.vue    # 主列表页面
├── index.vue                           # 入口页面
├── modules/
│   └── CrmCommissionCalculationModal.vue # 编辑模态框
└── README.md                           # 说明文档
```

### 使用方法

1. **查询数据**
   - 在搜索表单中输入查询条件
   - 点击查询按钮获取结果

2. **导出Excel**
   - 设置查询条件（可选）
   - 点击"导出"按钮下载Excel文件

3. **数据管理**
   - 支持新增、编辑、删除佣金计算记录
   - 支持批量删除操作

## 数据库设计

### 主要表结构

**crm_commission_calculation (佣金计算表)**
- `policy_no`: 保单号
- `calculation_date`: 计算日期
- `plan_name`: 产品英文名称
- `year_num`: 佣金期数
- `premium_amount`: 保费金额
- `commission_amount`: 佣金金额
- `payment_currency`: 缴费币种
- `contract_rate`: 合约手续费率
- `commission_rate`: 销售员佣金率
- `rc_exchange_rate`: 佣金结算日当天汇率

**关联表**
- `crm_policies`: 保单表
- `crm_policy_versions`: 保单版本表（获取保单生效日期）

## 注意事项

1. 保单生效日期优先使用 `calculation_date`，如果为空则使用保单版本表的 `effective_date`
2. 导出功能会导出所有符合条件的数据，不受分页限制
3. 金额字段显示时会自动格式化为货币格式
4. 百分比字段（费率）会自动转换为百分比显示

## 权限配置

需要在系统中配置相应的菜单权限：
- 查询权限: `crmcommissioncalculation:query`
- 导出权限: `crmcommissioncalculation:export`
- 新增权限: `crmcommissioncalculation:add`
- 编辑权限: `crmcommissioncalculation:edit`
- 删除权限: `crmcommissioncalculation:delete`
