package org.jeecg.modules.crmfy.crmcommissioncalculation.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.crmfy.crmcommissioncalculation.entity.CrmCommissionCalculation;
import org.jeecg.modules.crmfy.crmcommissioncalculation.vo.CrmCommissionCalculationQueryVO;
import org.jeecg.modules.crmfy.crmcommissioncalculation.vo.CrmCommissionCalculationQueryPO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

/**
 * @Description: 佣金计算表
 * @Author: jeecg-boot
 * @Date:   2025-04-11
 * @Version: V1.0
 */
public interface CrmCommissionCalculationMapper extends BaseMapper<CrmCommissionCalculation> {

    /**
     * 分页查询佣金计算信息
     * @param page 分页参数
     * @param queryPO 查询条件
     * @return 分页结果
     */
    IPage<CrmCommissionCalculationQueryVO> queryCommissionCalculationInfo(Page<CrmCommissionCalculationQueryVO> page, @Param("query") CrmCommissionCalculationQueryPO queryPO);

}
