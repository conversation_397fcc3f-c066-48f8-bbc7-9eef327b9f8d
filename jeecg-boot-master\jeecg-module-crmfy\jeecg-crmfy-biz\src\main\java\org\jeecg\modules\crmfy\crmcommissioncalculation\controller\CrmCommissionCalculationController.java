package org.jeecg.modules.crmfy.crmcommissioncalculation.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.crmfy.crmcommissioncalculation.entity.CrmCommissionCalculation;
import org.jeecg.modules.crmfy.crmcommissioncalculation.service.ICrmCommissionCalculationService;
import org.jeecg.modules.crmfy.crmcommissioncalculation.vo.CrmCommissionCalculationQueryVO;
import org.jeecg.modules.crmfy.crmcommissioncalculation.vo.CrmCommissionCalculationQueryPO;
import java.util.Date;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

 /**
 * @Description: 佣金计算表
 * @Author: jeecg-boot
 * @Date:   2025-04-11
 * @Version: V1.0
 */
@Slf4j
@Api(tags="佣金计算表")
@RestController
@RequestMapping("/crmcommissioncalculation/crmCommissionCalculation")
public class CrmCommissionCalculationController extends JeecgController<CrmCommissionCalculation, ICrmCommissionCalculationService> {
	@Autowired
	private ICrmCommissionCalculationService crmCommissionCalculationService;
	
	/**
	 * 分页列表查询
	 *
	 * @param crmCommissionCalculation
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@AutoLog(value = "佣金计算表-分页列表查询")
	@ApiOperation(value="佣金计算表-分页列表查询", notes="佣金计算表-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(CrmCommissionCalculation crmCommissionCalculation,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<CrmCommissionCalculation> queryWrapper = QueryGenerator.initQueryWrapper(crmCommissionCalculation, req.getParameterMap());
		Page<CrmCommissionCalculation> page = new Page<CrmCommissionCalculation>(pageNo, pageSize);
		IPage<CrmCommissionCalculation> pageList = crmCommissionCalculationService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 * 添加
	 *
	 * @param crmCommissionCalculation
	 * @return
	 */
	@AutoLog(value = "佣金计算表-添加")
	@ApiOperation(value="佣金计算表-添加", notes="佣金计算表-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody CrmCommissionCalculation crmCommissionCalculation) {
		crmCommissionCalculationService.save(crmCommissionCalculation);
		return Result.OK("添加成功！");
	}
	
	/**
	 * 编辑
	 *
	 * @param crmCommissionCalculation
	 * @return
	 */
	@AutoLog(value = "佣金计算表-编辑")
	@ApiOperation(value="佣金计算表-编辑", notes="佣金计算表-编辑")
	@RequestMapping(value = "/edit", method = RequestMethod.POST)
	public Result<?> edit(@RequestBody CrmCommissionCalculation crmCommissionCalculation) {
		crmCommissionCalculationService.updateById(crmCommissionCalculation);
		return Result.OK("编辑成功!");
	}
	
	/**
	 * 通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "佣金计算表-通过id删除")
	@ApiOperation(value="佣金计算表-通过id删除", notes="佣金计算表-通过id删除")
	@GetMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		crmCommissionCalculationService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 * 批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "佣金计算表-批量删除")
	@ApiOperation(value="佣金计算表-批量删除", notes="佣金计算表-批量删除")
	@GetMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.crmCommissionCalculationService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功！");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "佣金计算表-通过id查询")
	@ApiOperation(value="佣金计算表-通过id查询", notes="佣金计算表-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		CrmCommissionCalculation crmCommissionCalculation = crmCommissionCalculationService.getById(id);
		return Result.OK(crmCommissionCalculation);
	}

	/**
	 * 分页查询佣金计算信息
	 *
	 * @param queryPO
	 * @param pageNo
	 * @param pageSize
	 * @return
	 */
	@AutoLog(value = "佣金计算表-分页查询佣金计算信息")
	@ApiOperation(value="佣金计算表-分页查询佣金计算信息", notes="佣金计算表-分页查询佣金计算信息")
	@GetMapping(value = "/queryCommissionInfo")
	public Result<?> queryCommissionInfo(CrmCommissionCalculationQueryPO queryPO,
										@RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
										@RequestParam(name="pageSize", defaultValue="10") Integer pageSize) {
		Page<CrmCommissionCalculationQueryVO> page = new Page<CrmCommissionCalculationQueryVO>(pageNo, pageSize);
		IPage<CrmCommissionCalculationQueryVO> pageList = crmCommissionCalculationService.queryCommissionCalculationInfo(page, queryPO);
		return Result.OK(pageList);
	}

  /**
   * 导出excel
   *
   * @param request
   * @param crmCommissionCalculation
   */
  @RequestMapping(value = "/exportXls")
  public ModelAndView exportXls(HttpServletRequest request, CrmCommissionCalculation crmCommissionCalculation) {
      return super.exportXls(request, crmCommissionCalculation, CrmCommissionCalculation.class, "佣金计算表");
  }

  /**
   * 导出佣金计算信息Excel
   *
   * @param request
   * @param response
   * @param queryPO
   */
  @AutoLog(value = "佣金计算表-导出佣金计算信息Excel")
  @ApiOperation(value="佣金计算表-导出佣金计算信息Excel", notes="佣金计算表-导出佣金计算信息Excel")
  @RequestMapping(value = "/exportCommissionInfoXls")
  public ModelAndView exportCommissionInfoXls(HttpServletRequest request, HttpServletResponse response, CrmCommissionCalculationQueryPO queryPO) {
      // 查询所有数据（不分页）
      Page<CrmCommissionCalculationQueryVO> page = new Page<CrmCommissionCalculationQueryVO>(1, Integer.MAX_VALUE);
      IPage<CrmCommissionCalculationQueryVO> pageList = crmCommissionCalculationService.queryCommissionCalculationInfo(page, queryPO);

      // 导出Excel
      ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
      mv.addObject(NormalExcelConstants.FILE_NAME, "保险公司账单列表");
      mv.addObject(NormalExcelConstants.CLASS, CrmCommissionCalculationQueryVO.class);
      mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("保险公司账单列表", "保险公司账单"));
      mv.addObject(NormalExcelConstants.DATA_LIST, pageList.getRecords());
      return mv;
  }

  /**
   * 通过excel导入数据
   *
   * @param request
   * @param response
   * @return
   */
  @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
  public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
      return super.importExcel(request, response, CrmCommissionCalculation.class);
  }

}
